<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import { TextEllipsis, showFailToast } from 'vant';
import 'vant/es/text-ellipsis/style'
import 'vant/es/toast/style'
import { useRequest, usePagination } from 'vue-request';
import { discountApi, bannerListApi, providerAdvertisementApi, tourismProductsApi } from '@haierbusiness-front/apis';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Scrollbar, A11y, Autoplay, FreeMode } from 'swiper/modules';
import { MerchantTypeConstant } from '@haierbusiness-front/common-libs';
import router from '../../router'
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import 'swiper/css/autoplay';
import 'swiper/css/free-mode';
import banner from '@/assets/image/discount/mobile/banner.png'
import right from '@/assets/image/discount/mobile/right.png'
import tag from '@/assets/image/discount/tag.png'
import discount from '@/assets/image/discount/mobile/discount.png'
import discountS from '@/assets/image/discount/mobile/discount-s.png'
import shanzhuang from '@/assets/image/discount/mobile/shanzhuang.png'
import nanhai from '@/assets/image/discount/mobile/nanhai.png'
import canghai from '@/assets/image/discount/mobile/canghai.png'
import price from '@/assets/image/discount/mobile/price.png'
import house from '@/assets/image/discount/mobile/house.png'
import jiadian from '@/assets/image/discount/mobile/jiadian.png'
import zhouji from '@/assets/image/discount/mobile/zhouji.png'

const currentRouter = ref()

const modules = ref([Navigation, A11y, Autoplay, FreeMode])
const jdUrl = import.meta.env.VITE_JD_PICTURE_URL
// 机票
const {
    data: flightData,
    run: flightListApiRun,
    loading: flightLoading,
} = useRequest(discountApi.list, {
    defaultParams: [
        {
            merType: MerchantTypeConstant.FLIGHT.code.toString()
        }
    ],
    manual: false
});

// 住宿酒店
const {
    data: hotelData,
    run: hotelListApiRun,
    loading: hotelLoading,
} = useRequest(discountApi.list, {
    defaultParams: [
        {
            merType: MerchantTypeConstant.SIGNING.code.toString()
        }
    ],
    manual: false
});

// 订餐酒店
const {
    data: restaurantData,
    run: restaurantListApiRun,
    loading: restaurantLoading,
} = useRequest(discountApi.list, {
    defaultParams: [
        {
            merType: MerchantTypeConstant.RESTAURANT.code.toString()
        }
    ],
    manual: false
});

// 京东商品
const {
    data: jdData,
    run: jdListApiRun,
    loading: jdLoading,
} = useRequest(discountApi.searchProducts, {
    defaultParams: [
        {
            pageIndex: Math.floor(Math.random() * 6),
            pageSize: 10
        }
    ],
    manual: false
});

const jdProduct = computed(() => {
    if (jdData.value) {
        return jdData.value.hitResult
    } else
        return []
})

const priceToYuan = (price: number) => {
    if (!price) return 0;
    let yuan = (price / 100).toFixed(2);
    return yuan;
}

// 活动
const {
    data: bannerData,
    run: bannerListApiRun,
    loading: bannerLoading,
} = useRequest(bannerListApi.banners, {
    defaultParams: [
        {
            showStatus: 1
        }
    ],
    manual: false
});

const bannerDataSource = computed(() => bannerData.value || []);


// 优选
const {
    data: providerData,
    run: providerListApiRun,
    loading: providerLoading,
} = usePagination(providerAdvertisementApi.query, {
    defaultParams: [
        {
            showStatus: 1
        }
    ],
    manual: false
});

const providerDataSource = computed(() => providerData.value?.records || []);

const openLink = (url: string) => {
    window.open(url)
}

const gotoUrlOrDetail = (data: { jumpLinkPc?: string, id?: number }, url: string) => {
    if (data.jumpLinkPc) {
        window.open(data.jumpLinkPc)
    } else {
        if (!data.id) {
            showFailToast('未对应信息！')
            return
        }
        const thisUrl = currentRouter.value.resolve({
            path: url,
        })
        window.open(thisUrl.href + '?id=' + data.id)
    }
}

const gotoRestaurantPC = () => {
    showFailToast({
        message: '请至PC端ihaier->我的场景->商务云->青岛订餐',
        wordBreak: 'break-word'
    })
}

//旅游产品详情
const {
    data,
    run: listApiRun,
} = useRequest(tourismProductsApi.list,{
    defaultParams: [
        {
            pageNum: 1,
            pageSize: 10,
        }
    ],
    manual: false
});
const dataSource = computed(() => data.value?.records || []);

onMounted(async () => {
    currentRouter.value = await router
    if(dataSource.value.length < data.value?.total){
        listApiRun({
            pageNum: 1,
            pageSize: data.value?.total,
        })
    }
})
const products = ref()
const handleView = async (id: number) => {
    await get(id)
    if (products.value.externalLinks) {
        window.open(products.value.externalLinks, '_blank');
    } else {
        currentRouter.value.push({ path: "/mobile/tourismProducts", query: { id: id } })
    }

}
const get = async (id: number) => {
    const data = await tourismProductsApi.get(id)
    if (data && data.id) {
        products.value = data
    }
}

</script>

<template>
    <div class="mobile-con">
        <div class="banner">
            <img :src="banner" class="cover banner-img" />
            <div class="desc-1">员工因私预定福利来了！</div>
            <div class="desc-2">个人预定也可享受企业大客户协议价格！</div>
        </div>



        <div class="flight">
            <div class="title-con">
                <div class="title">
                    <div class="desc">特惠航司</div>
                    <div class="desc-bottom"></div>
                </div>
                <div class="more"
                    @click="openLink('https://travelservice.haier.net/fcopen/fcsso?requestFlag=turn&skipType=010000')">
                    <div class="more-font">更多优惠</div>
                    <img :src="right" class="right" />
                </div>
            </div>
            <div class="body height">
                <swiper v-if="flightData && flightData.length > 0" class="swiper-width height" :modules="modules"
                    :slides-per-view="'auto'" :space-between="17"
                    :loop="flightData && flightData.length > 6 ? true : false">
                    <swiper-slide class="swiper-slide" v-for="(item, index) in flightData" :key="index"
                        @click="openLink('https://travelservice.haier.net/fcopen/fcsso?requestFlag=turn&skipType=010000')">
                        <div class="merchant-img">
                            <img :src="item.icon" class="cover" />
                        </div>
                        <div class="discount-desc flex">
                            <div class="desc-discount flex">
                                {{ item.discountDesc }}
                            </div>
                            <img :src="tag" class="discount-image" />
                        </div>
                    </swiper-slide>
                </swiper>
            </div>
        </div>

        <div class="hotel">
            <div class="title-con">
                <div class="title">
                    <div class="desc">特惠酒店</div>
                    <div class="desc-bottom"></div>
                </div>
                <div class="more"
                    @click="openLink('https://travelservice.haier.net/fcopen/fcsso?requestFlag=turn&skipType=030000')">
                    <div class="more-font">更多优惠</div>
                    <img :src="right" class="right" />
                </div>
            </div>
            <div class="body height">
                <swiper v-if="hotelData && hotelData.length > 0" class="swiper-width height" :modules="modules"
                    :slides-per-view="'auto'" :space-between="17"
                    :loop="hotelData && hotelData.length > 6 ? true : false">
                    <swiper-slide class="swiper-slide" v-for="(item, index) in hotelData" :key="index"
                        @click="openLink('https://travelservice.haier.net/fcopen/fcsso?requestFlag=turn&skipType=030000')">
                        <div class="merchant-img">
                            <img :src="item.icon" class="cover" />
                            <div class="hanzao">含早</div>
                        </div>
                        <div class="flex img-top">
                            <img :src="discount" class="discount-image" />
                        </div>
                        <div class="flex">
                            <div class="desc flex">
                                {{ item.discountDesc }}
                            </div>
                        </div>
                    </swiper-slide>
                </swiper>
            </div>
        </div>

        <div class="jd">
            <div class="title-con">
                <div class="title">
                    <div class="desc">特惠全球购</div>
                    <div class="desc-bottom"></div>
                </div>
                <div class="more" @click="openLink('https://businesstravel.haier.net/wxmall/?state=&#/')">
                    <div class="more-font">更多优惠</div>
                    <img :src="right" class="right" />
                </div>
            </div>
            <div class="body height">
                <swiper v-if="jdProduct && jdProduct.length > 0" class="swiper-width height" :modules="modules"
                    :slides-per-view="'auto'" :space-between="17"
                    :loop="jdProduct && jdProduct.length > 6 ? true : false">
                    <swiper-slide class="swiper-slide" v-for="(item, index) in jdProduct" :key="index"
                        @click="openLink('https://businesstravel.haier.net/wxmall/?state=&#/')">
                        <div class="merchant-img">
                            <img :src="jdUrl + item.imageUrl" class="cover" />
                            <div class="merchant-title-back"></div>
                            <div class="merchant-title">
                                <text-ellipsis class="ellipsis" :content="item.wareName" />
                            </div>
                        </div>
                        <div class="flex img-top">
                            <img :src="price" class="discount-image" />
                        </div>
                        <div class="flex">
                            <div class="desc flex">
                                <span class="price-icon">¥</span>{{ priceToYuan(item.price) }}
                            </div>
                        </div>
                        <div class="flex price-jd">
                            京东¥{{ priceToYuan(item.jdPrice) }}
                        </div>
                    </swiper-slide>
                </swiper>
            </div>
        </div>

        <div class="ziying">
            <div class="title-con">
                <div class="title">
                    <div class="desc">自营酒店</div>
                    <div class="desc-bottom"></div>
                </div>
                <div class="more" @click="gotoRestaurantPC()">
                    <div class="more-font">更多优惠</div>
                    <img :src="right" class="right" />
                </div>
            </div>
            <div class="body height">
                <swiper class="swiper-width height" :modules="modules" :slides-per-view="'auto'" :space-between="17">
                    <swiper-slide class="swiper-slide" @click="gotoRestaurantPC()">
                        <div class="merchant-img">
                            <img :src="shanzhuang" class="cover" />
                            <div class="merchant-title-back"></div>
                            <div class="merchant-title yaHei">
                                海尔山庄
                            </div>
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-slide" @click="gotoRestaurantPC()">
                        <div class="merchant-img">
                            <img :src="canghai" class="cover" />
                            <div class="merchant-title-back"></div>
                            <div class="merchant-title yaHei">
                                沧海之粟
                            </div>
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-slide" @click="gotoRestaurantPC()">
                        <div class="merchant-img">
                            <img :src="nanhai" class="cover" />
                            <div class="merchant-title-back"></div>
                            <div class="merchant-title yaHei">
                                南海德音(海南路)
                            </div>
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-slide" @click="gotoRestaurantPC()">
                        <div class="merchant-img">
                            <img :src="zhouji" class="cover" />
                            <div class="merchant-title-back"></div>
                            <div class="merchant-title yaHei">
                                海尔洲际酒店
                            </div>
                        </div>
                    </swiper-slide>
                </swiper>
            </div>
        </div>

        <div class="restaurant">
            <div class="title-con">
                <div class="title">
                    <div class="desc">青岛用餐</div>
                    <div class="desc-bottom"></div>
                </div>
                <div class="more" @click="gotoRestaurantPC()">
                    <div class="more-font">更多优惠</div>
                    <img :src="right" class="right" />
                </div>
            </div>
            <div class="body height">
                <swiper class="swiper-width height" :modules="modules" :slides-per-view="'auto'" :space-between="17"
                    :loop="restaurantData && restaurantData.length > 6 ? true : false">
                    <swiper-slide class="swiper-slide" v-for="(item, index) in restaurantData" :key="index"
                        @click="gotoRestaurantPC()">
                        <div class="merchant-img">
                            <img :src="item.icon" class="cover" />
                            <div class="merchant-title-back"></div>
                            <div class="merchant-title">
                                <text-ellipsis class="ellipsis" :content="item.merName" />
                            </div>
                        </div>
                        <div class="flex discount-desc">
                            <div class="desc flex">
                                <img :src="discountS" class="tag" />
                                <div class="font">
                                    {{ item.discountDesc }}
                                </div>
                            </div>
                        </div>
                    </swiper-slide>
                </swiper>
            </div>
        </div>
        <div class="lvyou">
            <div class="title-con">
                <div class="title">
                    <div class="desc">旅游产品</div>
                    <div class="desc-bottom"></div>
                </div>
                <div class="more" @click="gotoRestaurantPC()">
                    <div class="more-font">更多产品</div>
                    <img :src="right" class="right" />
                </div>
            </div>
            <div class="body height">
                <swiper class="swiper-width height" :modules="modules" :slides-per-view="'auto'" :space-between="17">
                    <swiper-slide class="swiper-slide" @click="handleView(item.id)" v-for="(item, index) in dataSource"
                        :key="index" >
                        <div class="merchant-img">
                            <img :src="item.path" class="img" />
                            <div class="merchant-title-back"></div>
                            <div class="merchant-title">
                                <text-ellipsis class="ellipsis" :content="item.title" style="width: 90%;"/>
                            </div>
                        </div>
                    </swiper-slide>
                </swiper>
            </div>
        </div>

        <!-- <div class="hd">
            <div class="title-con">
                <div class="title">
                    <div class="desc">汇达云商</div>
                    <div class="desc-bottom"></div> 
                </div>
                <div class="more">
                    <div class="more-font">更多优惠</div>
                    <img :src="right" class="right" />
                </div>
            </div>
            <div class="body height">
                <swiper
                    v-if="jdProduct && jdProduct.length > 0"
                    class="swiper-width height"
                    :modules="modules"
                    :slides-per-view="'auto'"
                    :space-between="17"
                    :loop="jdProduct && jdProduct.length > 6 ? true : false"
                    :autoplay="false"
                >
                    <swiper-slide class="swiper-slide" v-for="(item, index) in jdProduct" :key="index">
                            <div class="merchant-img">
                                <img :src="jdUrl + item.imageUrl" class="cover" />
                                <div class="merchant-title-back"></div>
                                <div class="merchant-title">
                                    <text-ellipsis class="ellipsis" :content="item.wareName" />
                                </div>
                            </div>
                            <div class="flex img-top">
                                <img :src="price" class="discount-image" />
                            </div>
                            <div class="flex">
                                <div class="desc flex">
                                    <span class="price-icon">¥</span>{{ priceToYuan(item.price) }}
                                </div>
                            </div>
                    </swiper-slide>
                </swiper>
            </div>
        </div> -->

        <div class="acitivity">
            <swiper v-if="bannerDataSource && bannerDataSource.length > 0" class="swiper-width height"
                :modules="modules" :slides-per-view="'auto'"
                :loop="bannerDataSource && bannerDataSource.length > 6 ? true : false" :autoplay="{
                    disableOnInteraction: false,
                    delay: 2000,
                    pauseOnMouseEnter: true,
                    stopOnLastSlide: false
                }">
                <swiper-slide class="swiper-slide" v-for="(item, index) in bannerDataSource" :key="index"
                    @click="gotoUrlOrDetail(item, '/travel/bannerDetail')">
                    <img :src="item.imgUrl" class="cover" />
                </swiper-slide>
            </swiper>
        </div>

        <div class="youxuan-con">
            <div class="youxuan">
                <swiper v-if="providerDataSource && providerDataSource.length > 0" class="swiper-width height"
                    :modules="modules" :slides-per-view="'auto'"
                    :loop="providerDataSource && providerDataSource.length > 6 ? true : false" :autoplay="{
                        disableOnInteraction: false,
                        delay: 2500,
                        pauseOnMouseEnter: true,
                        stopOnLastSlide: false
                    }">
                    <swiper-slide class="swiper-slide" v-for="(item, index) in providerDataSource" :key="index"
                        @click="gotoUrlOrDetail(item, '/travel/adDetail')">
                        <img :src="item.imgUrl" class="cover" />
                    </swiper-slide>
                </swiper>
            </div>
            <div class="neigou">
                <div class="title">内购权益</div>
                <div class="img-con">
                    <div class="image"
                        @click="openLink('https://applink.feishu.cn/client/web_url/open?url=https%3A%2F%2Fm.ehaier.com%2Fsgmobile%2FinternalChannel%3Fop_platform_service%3Dhide_navigator&mode=window&brand_t=ihaier')">
                        <img :src="jiadian" class="cover" />
                        <div class="image-title">家电内购</div>
                    </div>
                    <div class="image" @click="openLink('https://hrcare.haier.net/mobile/housingResource')">
                        <img :src="house" class="cover" />
                        <div class="image-title">房子内购</div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</template>

<style lang="less">
*{
    font-family: "Microsoft YaHei" !important;
}
.cover {
    width: 100%;
    height: 100%;
}

.flex {
    display: flex;
}

.yaHei {
    font-family: "Microsoft YaHei" !important;
}

.mobile-con {
    display: flex;
    width: 750px;
    min-height: 100vh;
    align-items: center;
    flex-direction: column;
    font-family: 'HarmonyBold';
    background: #F5F5F5;
    padding-bottom: constant(safe-area-inset-bottom);
    /* 兼容 iOS < 11.2 */
    padding-bottom: env(safe-area-inset-bottom);
    /* 兼容 iOS >= 11.2 */

    .title-con {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 52px;

        .title {
            display: flex;
            width: 160px;
            height: 37px;
            position: relative;

            .desc {
                position: absolute;
                width: 160px;
                height: 32px;
                font-weight: 500;
                font-size: 32px;
                color: #262626;
                z-index: 1;
            }

            .desc-bottom {
                position: absolute;
                width: 128px;
                height: 12px;
                background: linear-gradient(270deg, rgba(236, 243, 255, 0) 0%, #75A5F3 100%);
                top: 25px;
                z-index: 0;
            }
        }

        .more {
            width: 168px;
            height: 52px;
            background: #FFFFFF;
            border-radius: 26px;
            margin-right: 23px;
            display: flex;
            justify-content: center;
            flex-direction: row;

            .more-font {
                font-weight: 400;
                font-size: 24px;
                color: #262626;
                margin-top: 1.5px;
            }

            .right {
                margin-left: 8px;
                width: 32px;
                height: 32px;
            }
        }
    }


    .banner {
        position: relative;
        margin-top: 20px;
        width: 686px;
        height: 118px;

        .desc-1 {
            position: absolute;
            font-size: 22px;
            color: #FFF8EE;
            line-height: 30px;
            text-shadow: 0px 1px 1px #CE3E00;
            z-index: 1;
            top: 24px;
            left: 24px;
        }

        .desc-2 {
            position: absolute;
            font-size: 28px;
            color: #FFF8EE;
            line-height: 40px;
            text-shadow: 0px 1px 1px #CF4500;
            z-index: 1;
            bottom: 19px;
            left: 24px;
        }

        .banner-img {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
        }
    }

    .flight {
        padding: 22px 0 32px 32px;
        margin-top: 20px;
        width: 686px;
        height: 286px;
        background: #FFFFFF;
        box-shadow: 0px 8px 13px 0px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        border: 2px solid #FFFFFF;

        .height {
            height: 176px;
        }

        .swiper-slide-height {
            height: 162px;
        }

        .discount-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 156px;
            height: 30px;
        }

        .discount-desc {
            margin-top: 4px;
            width: 156px;
            height: 30px;
            margin-left: 16px;
            flex-direction: row-reverse;
            position: relative;
        }

        .desc-discount {
            width: 107px;
            padding: 3 6px;
            justify-content: center;
            font-size: 20px;
            font-weight: 600;
            color: #FF3000;
            line-height: 30px;
            z-index: 1;
        }

        .merchant-img {
            width: 188px;
            height: 128px;
        }

    }

    .hotel {
        padding: 22px 0 32px 32px;
        margin-top: 20px;
        width: 686px;
        height: 333px;
        background: #FFFFFF;
        box-shadow: 0px 8px 13px 0px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        border: 2px solid #FFFFFF;

        .height {
            height: 230px;
        }

        .swiper-slide-height {
            height: 209px;
        }

        .discount-image {
            width: 127px;
            height: 37px;
        }

        .desc {
            width: 100%;
            font-weight: 500;
            font-size: 32px;
            color: #FF3000;
        }

        .img-top {
            margin-top: 12px;
        }

        .merchant-img {
            width: 188px;
            height: 128px;
            position: relative;

            .hanzao {
                position: absolute;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 54px;
                height: 28px;
                top: 6px;
                right: 6px;
                background: linear-gradient(141deg, #FF8D2E 0%, #FF0D0F 100%);
                border-radius: 14px;
                font-size: 20px;
                color: #FFFFFF;
            }
        }
    }

    .ziying {
        padding: 22px 0 32px 32px;
        margin-top: 20px;
        width: 686px;
        height: 292px;
        background: #FFFFFF;
        box-shadow: 0px 8px 13px 0px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        border: 2px solid #FFFFFF;

        .merchant-img {
            width: 188px;
            height: 128px;
            position: relative;
        }

        .merchant-title-back {
            position: absolute;
            height: 42px;
            width: 100%;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
            border-radius: 0px 0px 12px 12px;
            bottom: 0;
        }

        .merchant-title {
            position: absolute;
            height: 42px;
            width: 100%;
            font-weight: 400;
            font-size: 22px;
            color: #FFFFFF;
            line-height: 33px;
            display: flex;
            justify-content: center;
            align-items: center;
            bottom: 0;
            z-index: 2;
        }
    }

    .lvyou {
        padding: 22px 0 32px 32px;
        margin-top: 20px;
        width: 686px;
        height: 292px;
        background: #FFFFFF;
        box-shadow: 0px 8px 13px 0px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        border: 2px solid #FFFFFF;

        .merchant-img {
            width: 188px;
            height: 168px;
            position: relative;
            img{
                width: 100%;
                height: 100%;
            }
        }

        .merchant-title-back {
            position: absolute;
            height: 42px;
            width: 100%;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
            border-radius: 0px 0px 12px 12px;
            bottom: 0;
        }

        .merchant-title {
            position: absolute;
            height: 42px;
            width: 100%;
            font-weight: 400;
            font-size: 22px;
            color: #FFFFFF;
            line-height: 33px;
            display: flex;
            justify-content: center;
            align-items: center;
            bottom: 0;
            z-index: 2;
        }
    }

    .restaurant {
        padding: 22px 0 32px 32px;
        margin-top: 20px;
        width: 686px;
        height: 336px;
        background: #FFFFFF;
        box-shadow: 0px 8px 13px 0px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        border: 2px solid #FFFFFF;

        .height {
            height: 230px;
        }

        .swiper-slide-height {
            height: 212px;
        }

        .discount-desc {
            display: flex;
            width: 188px;
            flex-direction: row;
            margin-top: 15px;

            .desc {
                align-items: center;
            }

            .tag {
                display: flex;
                width: 60px;
                height: 28px;
            }

            .font {
                margin-left: 8px;
                font-weight: 500;
                font-size: 32px;
                color: #FB4105;
            }


        }

        .merchant-img {
            width: 188px;
            height: 168px;
            position: relative;


            .merchant-title-back {
                position: absolute;
                height: 42px;
                width: 100%;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
                border-radius: 0px 0px 12px 12px;
                bottom: 0;
            }

            .merchant-title {
                position: absolute;
                height: 42px;
                width: 100%;
                font-weight: 400;
                font-size: 22px;
                color: #FFFFFF;
                line-height: 33px;
                display: flex;
                justify-content: center;
                align-items: center;
                bottom: 0;
                z-index: 1;

                .ellipsis {
                    width: 90% !important;
                    font-family: "Microsoft YaHei";
                    text-align: center;
                }
            }
        }
    }

    .jd {
        padding: 22px 0 32px 32px;
        margin-top: 20px;
        width: 686px;
        height: 397px;
        background: #FFFFFF;
        box-shadow: 0px 8px 13px 0px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        border: 2px solid #FFFFFF;

        .height {
            height: 303px;
        }

        .swiper-slide-height {
            height: 273px;
        }

        .discount-image {
            width: 71px;
            height: 37px;
        }

        .desc {
            width: 100%;
            font-weight: 500;
            font-size: 32px;
            color: #FF3000;
            display: flex;
            align-items: baseline;

            .price-icon {
                font-size: 20px;
            }
        }

        .price-jd {
            font-weight: 400;
            font-size: 20px;
            color: #BFBFBF;
            text-decoration-line: line-through;
        }

        .img-top {
            margin-top: 12px;
        }

        .merchant-img {
            position: relative;
            width: 188px;
            height: 168px;
        }

        .merchant-title-back {
            position: absolute;
            height: 42px;
            width: 100%;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
            border-radius: 0px 0px 12px 12px;
            bottom: 0;
        }

        .merchant-title {
            position: absolute;
            height: 42px;
            width: 100%;
            font-weight: 400;
            font-size: 22px;
            color: #FFFFFF;
            line-height: 33px;
            display: flex;
            justify-content: center;
            align-items: center;
            bottom: 0;
            z-index: 1;

            .ellipsis {
                width: 90%;
                font-family: "Microsoft YaHei";
                text-align: center;
            }
        }
    }

    .hd {
        padding: 22px 0 32px 32px;
        margin-top: 20px;
        width: 686px;
        height: 373px;
        background: #FFFFFF;
        box-shadow: 0px 8px 13px 0px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        border: 2px solid #FFFFFF;

        .height {
            height: 281px;
        }

        .swiper-slide-height {
            height: 249px;
        }

        .discount-image {
            width: 71px;
            height: 37px;
        }

        .desc {
            width: 100%;
            font-weight: 500;
            font-size: 32px;
            color: #FF3000;
            display: flex;
            align-items: baseline;

            .price-icon {
                font-size: 20px;
            }
        }

        .price-jd {
            font-weight: 400;
            font-size: 20px;
            color: #BFBFBF;
            text-decoration-line: line-through;
        }

        .img-top {
            margin-top: 12px;
        }

        .merchant-img {
            position: relative;
            width: 188px;
            height: 168px;
        }

        .merchant-title-back {
            position: absolute;
            height: 42px;
            width: 100%;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
            border-radius: 0px 0px 12px 12px;
            bottom: 0;
        }

        .merchant-title {
            position: absolute;
            height: 42px;
            width: 100%;
            font-weight: 400;
            font-size: 22px;
            color: #FFFFFF;
            line-height: 33px;
            display: flex;
            justify-content: center;
            align-items: center;
            bottom: 0;
            z-index: 1;

            .ellipsis {
                width: 90%;
                font-family: "Microsoft YaHei";
            }
        }
    }

    .body {
        width: 650px;
        margin-top: 18px;

        .swiper-width {
            width: 650px;

            .swiper-slide {
                width: 188px;
                overflow: hidden;
                background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
                border-radius: 12px;
                opacity: 0.8;
                backdrop-filter: blur(8px);
            }
        }
    }

    .acitivity {
        width: 686px;
        height: 189.41px;
        border-radius: 12px;
        border: 2px solid #FFFFFF;
        margin-top: 20px;
        overflow: hidden;

        .swiper-slide {
            width: 686px;
            height: 189.41px;
        }

    }

    .youxuan-con {
        margin-top: 20px;
        margin-bottom: 40px;
        width: 686px;
        height: 234px;
        display: flex;
        flex-direction: row;
        justify-content: space-around;

        .youxuan {
            width: 333px;
            height: 234px;
            border-radius: 12px;
            border: 2px solid #FFFFFF;
            box-shadow: 0px 8px 13px 0px rgba(0, 0, 0, 0.06);
            overflow: hidden;

            .swiper-slide {
                width: 333px;
                height: 234px;
            }
        }

        .neigou {
            width: 333px;
            height: 234px;
            border-radius: 12px;
            box-shadow: 13px 8px 13px 0px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            padding: 20px;
            background: #FFFFFF;

            .title {
                font-weight: 500;
                font-size: 32px;
                color: #262626;
                line-height: 32px;
            }

            .img-con {
                display: flex;
                width: 100%;
                height: 142px;
                margin-top: 20px;
                flex-direction: row;
                justify-content: space-around;

                .image {
                    display: flex;
                    width: 142px;
                    height: 142px;
                    position: relative;

                    .image-title {
                        position: absolute;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 128px;
                        height: 38px;
                        background: rgba(0, 0, 0, 0.6);
                        border-radius: 19px;
                        font-size: 22px;
                        color: #FFFFFF;
                        line-height: 30px;
                        left: 7px;
                        bottom: 9px;
                    }
                }
            }
        }

    }


}


:root:root {
    --van-toast-default-width: 185px;
}
</style>

<style>
.swiper-free-mode>.swiper-wrapper {
    -webkit-transition-timing-function: linear;
    /*之前是ease-out*/
    -moz-transition-timing-function: linear;
    -ms-transition-timing-function: linear;
    -o-transition-timing-function: linear;
    transition-timing-function: linear;
    margin: 0 auto;
}
</style>