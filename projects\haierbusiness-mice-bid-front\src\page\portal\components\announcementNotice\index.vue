<script setup lang="ts">
import { announcementNoticeApi } from '@haierbusiness-front/apis';
import {
  AnnouncementContentForm,
  IAnnouncementNotice
} from '@haierbusiness-front/common-libs';

import { computed, ref, onMounted, nextTick, reactive, h, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import EditDialog from './edit-dialog.vue'
import router from '../../../../router';
import { ColumnType } from 'ant-design-vue/es/table';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router

  // 初始加载数据
  listApiRun({
    effectScope: 2,
    pageNum: 1,
    pageSize: 10
  });
})
const confirmLoading = ref(false);
const {
  data,
  run: listApiRun,
  loading,
} = usePagination(announcementNoticeApi.list);
//可见
const visible = ref(false)
//详情数据
const editData = ref<IAnnouncementNotice>()
//通知详情
const noticeDetails = async (id: number) => {
  if (!id) return;

  confirmLoading.value = true;
  try {
    const res = await announcementNoticeApi.details(id);
    editData.value = res;
    if (editData.value) {
      console.log(editData.value);
      if (res.contentForm == 2) {
        window.open(res.informContent, '_blank');
      } else {
        visible.value = true
      }

    }
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    confirmLoading.value = false;
  }
}

const onDialogClose = () => {
  visible.value = false
}
const handleOk = () => {
  visible.value = false
}

const columns: ColumnType[] = [
  {
    title: '通知标题',
    dataIndex: 'title',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '120px',
    fixed: 'right',
    align: 'center'
  },
];

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pagination: any,
) => {
  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  };
  listApiRun(params);
};

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
      :pagination="pagination" :scroll="{ y: 500 }" :loading="loading" @change="handleTableChange">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === '_operator'">
          <h-button type="link" @click="noticeDetails(record.id)">查看</h-button>
        </template>
      </template>
    </h-table>
  </div>
  <div v-if="visible">
    <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
    </edit-dialog>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.notice-title {
  width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: black;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    color: #1677ff;
  }
}
</style>
