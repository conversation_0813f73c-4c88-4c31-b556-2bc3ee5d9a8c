<script setup lang="ts">
import {
  Button as hButton,
  Input as hInput,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  DatePicker as hDatePicker,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { annualBudgetApi } from '@haierbusiness-front/apis';
import {
  IAnnualBudgetFilter,
  IAnnualBudget,
  hotelLevelConstant,
  hotelLevelAllConstant
} from '@haierbusiness-front/common-libs';
import { formatNumberThousands } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, nextTick } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../router'
import { title } from 'process';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })
})

const columns: ColumnType[] = [
  {
    title: '会议名称',
    dataIndex: 'name',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '会议月份',
    dataIndex: 'miceTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => dayjs(text).format('YYYY.M')
  },
  {
    title: '会议天数',
    dataIndex: 'day',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text != null ? `${text}天` : '',
  },
  {
    title: '会议地点',
    dataIndex: 'place',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '酒店星级',
    dataIndex: 'hotelLevel',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => hotelLevelAllConstant.ofType(text)?.desc || ''
  },
  {
    title: '预算金额',
    dataIndex: 'budget',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text != null ? `${formatNumberThousands(text)} 元` : '',
  },
  {
    title: '参数人数',
    dataIndex: 'personTotal',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text != null ? `${text}人` : '',
  },
  {
    title: '是否布展',
    dataIndex: 'isCloth',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text ? '是' : '否'
  },
  {
    title: '需求项目',
    dataIndex: 'item',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return options
        .filter(opt => (text & opt.value) === Number(opt.value))
        .map(opt => opt.label)
        .join(',')
    }
  },
  {
    title: '会议描述',
    dataIndex: 'description',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属年份',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text, record }) => {
      const createYear = new Date(text).getFullYear()
      const miceYear = new Date(record.miceTime).getFullYear()
      return createYear === miceYear ? createYear : `${createYear}≠${miceYear}`
    }
  },
  {
    title: '提报人',
    dataIndex: 'createName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '提报时间',
    dataIndex: 'gmtCreate',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IAnnualBudgetFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(annualBudgetApi.list);

const reset = () => {
  searchParam.value = {}
  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  // 创建表单数据副本，避免直接修改原始数据
  let formData = { ...searchParam.value };
  if (searchParam.value.miceTime) {
    formData.miceTime = `${searchParam.value.miceTime}-01`;
  }
  if(searchParam.value.hotelLevel){
    formData = processHotelLevel(formData)
  }
  
  console.log(formData, "formData");

  listApiRun({
    ...formData,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const processHotelLevel = (params: IAnnualBudgetFilter) => {
  const formData = { ...params };
  const currentLevel = Number(params?.hotelLevel);
  
  if (!isNaN(currentLevel)) {
    const matchedCodes = (hotelLevelAllConstant.toArray() || [])
      .filter(item => {
        const targetDesc = hotelLevelAllConstant.ofType(currentLevel)?.desc ?? '';
        return item?.desc?.includes(targetDesc);
      })
      .map(item => item.code);
    
    formData.hotelLevel = [
      ...(Array.isArray(formData.hotelLevel) ? formData.hotelLevel : []),
      ...matchedCodes
    ];
  }
  return formData;
}


const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IAnnualBudget, IAnnualBudget>(annualBudgetApi, "年度会议", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))


const handleDetails = (item: IAnnualBudget, val: number) => {
  currentRouter.value.push({
    path: '/bidman/annualBudget/details',
    query: {
      id: item.id,
    }
  })
}

const options = [
  { value: '1', label: '住宿' },
  { value: '2', label: '餐饮' },
  { value: '4', label: '会场' },
  { value: '8', label: '用车' },
]


const popupScroll = () => {
  console.log('popupScroll');
};


</script>

<template>
  <div class="container">
    <div style="background-color: #fff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
      <h-row :align="'middle'">
        <h-col :span="24" style="margin-bottom: 10px;">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="createTime">会议名称：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="examineCode" v-model:value="searchParam.name" placeholder="请输入" allow-clear
                :maxlength="500" />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="createTime">会议月份：</label>
            </h-col>
            <h-col :span="4">
              <h-date-picker v-model:value="searchParam.miceTime" style="width: 100%" picker="month" format="YYYY-MM"
                value-format="YYYY-MM" placeholder="请选择会议月份" allow-clear/>
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="createTime">会议地点：</label>
            </h-col>
            <h-col :span="4">
              <h-input id="examineCode" v-model:value="searchParam.place" placeholder="请输入" allow-clear
                :maxlength="500" />
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="createTime">酒店星级：</label>
            </h-col>
            <h-col :span="4">
              <h-select v-model:value="searchParam.hotelLevel" placeholder="请选择酒店星级" style="width: 100%" allow-clear> 
                <h-select-option v-for="item in hotelLevelConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </h-select-option>
              </h-select>
            </h-col>
          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="createTime">是否布展：</label>
            </h-col>
            <h-col :span="4">
              <h-select id="examineState" v-model:value="searchParam.isCloth" placeholder="请选择状态" class="full-width"
                allow-clear>
                <h-select-option :value="true">是</h-select-option>
                <h-select-option :value="false">否</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right;padding-right: 10px;">
              <label for="createTime">需求项目：</label>
            </h-col>
            <h-col :span="4">
              <h-select v-model:value="searchParam.items" :options="options" mode="multiple" placeholder="请选择需求项目"
                style="width: 100%" @popupScroll="popupScroll" allow-clear></h-select>
            </h-col>

          </h-row>
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;margin-bottom: 20px;">
            <h-col :span="24" style="text-align: right;">
              <h-button style="margin-right: 10px" @click="reset">重置</h-button>
              <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
                <SearchOutlined />查询
              </h-button>
            </h-col>
          </h-row>
        </h-col>
        <h-col :span="24">
          <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
            :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === '_operator'">
                <h-button type="link" @click="handleDetails(record, 2)">查看</h-button>
              </template>
            </template>
          </h-table>
        </h-col>
      </h-row>

      <div v-if="visible">
        <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
        </edit-dialog>
      </div>

    </div>
  </div>

</template>

<style scoped lang="less">
.container{
  padding: 15px;
  background-color: #fff;
  height: 100%;
}
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.full-width {
  width: 100%;
}
</style>
