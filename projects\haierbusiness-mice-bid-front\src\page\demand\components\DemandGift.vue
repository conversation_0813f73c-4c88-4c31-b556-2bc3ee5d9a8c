<script setup lang="ts">
// 礼品需求
import {
  Form as hForm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  Input as hInput,
  InputNumber as hInputNumber,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  Table as hTable,
  Modal as hModal,
  Tooltip as hTooltip,
  Spin as hSpin,
  DatePicker as hDatePicker,
  message,
} from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, defineExpose } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import { DemandSubmitObj, PresentsArr } from '@haierbusiness-front/common-libs';
import { demandApi, pascalCaseApi } from '@haierbusiness-front/apis';
import GiftSelectionModal from '@haierbusiness-front/components/mice/giftSelectionModal/index.vue';

const props = defineProps({
  cacheStr: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['demandPresentFunc']);

const demandPresentsFormRef = ref();

// 礼品需求表单
const formState = reactive<DemandSubmitObj>({
  // 礼品需求
  presents: [],
});
// 礼品需求
const presentParams = ref<PresentsArr>({
  // 需求礼品需求明细
  deliveryDate: '', // 送达日期
  demandTotalPrice: 0, // 总预算
  unit: '', // 单位
  personNum: null, // 礼品数量
  personSpecs: '以服务商提报为准', // 礼品说明

  productName: '', // 产品名称,当未选择产品时可自由修改
  productId: 0, // 礼品产品id
  productMerchantId: 0, // 产品所属商户id

  calcTotalPrice: 0, // 测算总预算
  selectedGiftPrice: 0, // 选择的礼品单价
  unitPrice: null, // 单价字段
  optionType: 0, // 选择类型 0:手动 1:自动
});

// 礼品选择相关状态
const showGiftModal = ref(false);
const currentGiftIndex = ref(0);

// 每个礼品项的模式状态
const giftModes = ref<Record<number, 'select' | 'manual'>>({});

// 锚点 - 用户点击添加需求，需跳转至添加位置
const anchorId = (id: string) => {
  document?.getElementById(id)?.scrollIntoView({
    behavior: 'smooth', //smooth:平滑，auto：直接定位
    block: 'center',
    inline: 'start',
  });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPresentsFormRef.value
    .validate()
    .then(() => {
      emit('demandPresentFunc', { ...formState });

      isVerifyPassed = true;
    })
    .catch((err: any) => {
      isVerifyPassed = false;

      anchorId('demandPresentId');
    });

  return isVerifyPassed;
};

// 暂存
const tempSave = () => {
  emit('demandPresentFunc', { ...formState });
};

defineExpose({ onSubmit, tempSave });

// 切换模式
const toggleGiftMode = (index: number, mode: 'select' | 'manual') => {
  const currentMode = giftModes.value[index] || 'manual';

  // 如果模式相同，不做处理
  if (currentMode === mode) {
    return;
  }

  // 更新模式
  giftModes.value[index] = mode;

  if (mode === 'select') {
    // 切换到选择模式时，清空手动输入的数据
    if (formState.presents && formState.presents[index]) {
      // 使用 Vue.set 或直接替换整个对象以确保响应式更新
      formState.presents[index] = {
        ...presentParams.value,
        productName: '',
        productId: 0,
        productMerchantId: 0,
        personNum: null,
        unit: '',
        demandTotalPrice: 0,
        deliveryDate: '',
        personSpecs: '以服务商提报为准',
        calcTotalPrice: 0,
        selectedGiftPrice: 0,
        optionType: 1, // 选择模式
      };
    }
  } else {
    // 切换到手动模式时，清空选择的产品信息
    if (formState.presents && formState.presents[index]) {
      // 使用 Vue.set 或直接替换整个对象以确保响应式更新
      formState.presents[index] = {
        ...presentParams.value,
        productName: '',
        productId: 0,
        productMerchantId: 0,
        personNum: null,
        unit: '',
        demandTotalPrice: 0,
        deliveryDate: '',
        personSpecs: '以服务商提报为准',
        calcTotalPrice: 0,
        selectedGiftPrice: 0,
        optionType: 0, // 手动模式
      };
    }
  }
};

// 处理输入框点击事件
const handleInputClick = (index: number) => {
  if ((giftModes.value[index] || 'manual') === 'select') {
    currentGiftIndex.value = index;
    showGiftModal.value = true;
  }
};

// 选择礼品回调
const handleSelectGift = (gift: any, index: number) => {
  if (formState.presents && formState.presents[index]) {
    // 自动填充表单字段
    formState.presents[index].productName = gift.presentName || '';
    formState.presents[index].productId = gift.id || 0;
    formState.presents[index].productMerchantId = gift.merchantId || 0;
    formState.presents[index].unit = '件'; // 默认单位
    formState.presents[index].personSpecs = gift.specComb || '以服务商提报为准';

    // 保存选择的礼品价格信息
    formState.presents[index].selectedGiftPrice = gift.selectedSku?.salePrice || gift.salePrice || 0;

    // 保存单价字段
    formState.presents[index].unitPrice = gift.unitPrice || gift.selectedSku?.salePrice || gift.salePrice || 0;

    // 如果已经输入了数量，自动计算总预算
    if (formState.presents[index].personNum && formState.presents[index].selectedGiftPrice) {
      formState.presents[index].demandTotalPrice =
        formState.presents[index].selectedGiftPrice * formState.presents[index].personNum;
    }
  }

  // 触发价格计算
  if (
    formState.presents &&
    formState.presents[index] &&
    formState.presents[index].personNum &&
    formState.presents[index].demandTotalPrice
  ) {
    calcPrice(index);
  }
};

// 不可选日期
const disabledDate = (current: Dayjs) => {
  return current && current < dayjs().endOf('day');
};

// 礼品需求 - 删除
const removeGood = (index: number) => {
  if (formState.presents) {
    formState.presents.splice(index, 1);
  }
  // 删除对应的模式状态
  delete giftModes.value[index];
  // 重新整理模式状态的索引
  const newModes: Record<number, 'select' | 'manual'> = {};
  Object.keys(giftModes.value).forEach((key) => {
    const keyNum = parseInt(key);
    if (keyNum > index) {
      newModes[keyNum - 1] = giftModes.value[keyNum];
    } else if (keyNum < index) {
      newModes[keyNum] = giftModes.value[keyNum];
    }
  });
  giftModes.value = newModes;
};
// 添加
const goodAdd = () => {
  if (!formState.presents) {
    formState.presents = [];
  }
  const newIndex = formState.presents.length;
  formState.presents.push(JSON.parse(JSON.stringify(presentParams.value)));
  // 为新添加的礼品初始化模式为手动模式
  giftModes.value[newIndex] = 'manual';
};

// 价格测算
const calcPrice = async (i: number) => {
  if (
    formState.presents &&
    formState.presents[i] &&
    formState.presents[i].personNum &&
    formState.presents[i].demandTotalPrice
  ) {
    const calcParams = {
      personNum: formState.presents[i].personNum, //
      demandTotalPrice: formState.presents[i].demandTotalPrice, // 总预算

      deliveryDate: formState.presents[i].deliveryDate ? formState.presents[i].deliveryDate : null, //
      personSpecs: formState.presents[i].personSpecs === '以服务商提报为准' ? null : formState.presents[i].personSpecs, //
      productName: formState.presents[i].productName, //
      productId: formState.presents[i].productId, //
      productMerchantId: formState.presents[i].productMerchantId, //
    };

    const res = await demandApi.priceCalcPresent({
      ...calcParams,
    });

    if (formState.presents && formState.presents[i] && res && 'calcTotalPrice' in res) {
      formState.presents[i].calcTotalPrice = (res as any).calcTotalPrice; //
    }

    emit('demandPresentFunc', { ...formState });
  }
};

// 数量变化时的处理
const handlePersonNumChange = (index: number) => {
  if (formState.presents && formState.presents[index]) {
    const item = formState.presents[index];

    // 如果是选择模式且有选择的礼品价格，自动计算总预算
    if ((giftModes.value[index] || 'manual') === 'select' && item.selectedGiftPrice && item.personNum) {
      item.demandTotalPrice = item.selectedGiftPrice * item.personNum;
    }

    // 触发价格测算
    calcPrice(index);
  }
};

onMounted(async () => {
  goodAdd();

  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);
    if (cacheObj.presents && Object.keys(cacheObj.presents).length > 0) {
      formState.presents = [...cacheObj.presents];
      // 为缓存的礼品项初始化模式状态 - 根据optionType设置
      formState.presents.forEach((item, index) => {
        // optionType: 0=手动, 1=自动/选择
        giftModes.value[index] = item.optionType === 1 ? 'select' : 'manual';
      });
    }
  }
});
</script>

<template>
  <!-- 礼品需求 -->
  <div class="demand_present demand_pad24">
    <div class="demand_title">
      <div class="demand_border"></div>
      <span>礼品需求</span>
      <span class="choose_tip ml16">当前产品为预览产品，实际产品与价格以最终方案为主</span>
    </div>

    <h-form
      class="mt20"
      ref="demandPresentsFormRef"
      :model="formState"
      :labelCol="{ style: { width: '84px' } }"
      hideRequiredMark
    >
      <div class="plan_col_list mb20" v-for="(goodItem, goodIndex) in formState.presents" :key="goodIndex">
        <div class="plan_col_title">
          {{ '礼品' + (goodIndex + 1) }}
        </div>
        <div v-show="goodIndex > 0" class="plan_col_del" @click="removeGood(goodIndex)"></div>

        <h-row :gutter="12" class="mt20">
          <h-col :span="8" class="flex" id="demandPresentId">
            <h-form-item
              class="gift_text"
              label="礼品类型："
              :name="['presents', goodIndex, 'productName']"
              :rules="{
                required: true,
                message: '请填写礼品类型',
                trigger: 'change',
              }"
            >
              <h-input
                v-model:value="goodItem.productName"
                placeholder="请填写礼品类型"
                :maxlength="200"
                :allow-clear="(giftModes[goodIndex] || 'manual') === 'manual'"
                @click="handleInputClick(goodIndex)"
                :readonly="(giftModes[goodIndex] || 'manual') === 'select'"
                :style="{
                  cursor: (giftModes[goodIndex] || 'manual') === 'select' ? 'pointer' : 'text',
                }"
              />
            </h-form-item>

            <div class="gift_select ml6">
              <h-select
                :value="giftModes[goodIndex] || 'manual'"
                @change="(value: string) => toggleGiftMode(goodIndex, value as 'select' | 'manual')"
                style="width: 80px"
              >
                <h-select-option value="manual">手动</h-select-option>
                <h-select-option value="select">选择</h-select-option>
              </h-select>
            </div>
          </h-col>

          <h-col :span="8">
            <h-form-item
              label="礼品数量："
              :name="['presents', goodIndex, 'personNum']"
              :rules="{
                required: true,
                message: '请填写礼品数量',
                trigger: 'change',
              }"
            >
              <h-input-number
                v-model:value="goodItem.personNum"
                @blur="handlePersonNumChange(goodIndex)"
                @change="handlePersonNumChange(goodIndex)"
                placeholder="请填写礼品数量"
                allow-clear
                :min="1"
                :max="999999"
                style="width: 100%"
              />
            </h-form-item>
          </h-col>

          <h-col :span="8">
            <h-form-item
              label="单位："
              :name="['presents', goodIndex, 'unit']"
              :rules="{
                required: true,
                message: '请填写单位',
                trigger: 'change',
              }"
            >
              <h-input v-model:value="goodItem.unit" placeholder="请填写单位" :maxlength="20" allow-clear />
            </h-form-item>
          </h-col>

          <h-col :span="8">
            <h-form-item
              label="总预算："
              :name="['presents', goodIndex, 'demandTotalPrice']"
              :rules="{
                required: true,
                message: '请填写总预算',
                trigger: 'change',
              }"
            >
            <!-- && !!goodItem.selectedGiftPrice -->
              <h-input-number
                v-model:value="goodItem.demandTotalPrice"
                @blur="calcPrice(goodIndex)"
                placeholder="请填写总预算"
                addon-after="元"
                allow-clear
                :min="1"
                :max="999999"
                :precision="2"
                style="width: 100%"
                :disabled="giftModes[goodIndex] === 'select'"
              />
            </h-form-item>
          </h-col>

          <h-col :span="8">
            <h-form-item
              label="送达日期："
              :name="['presents', goodIndex, 'deliveryDate']"
              :rules="{
                required: true,
                message: '请选择送达日期',
                trigger: 'change',
              }"
            >
              <h-date-picker
                style="width: 100%"
                v-model:value="goodItem.deliveryDate"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
              />
            </h-form-item>
          </h-col>

          <h-col :span="16">
            <h-form-item
              label="规格描述："
              :name="['presents', goodIndex, 'personSpecs']"
              :rules="{
                required: true,
                message: '请填写规格描述',
                trigger: 'change',
              }"
            >
              <h-input v-model:value="goodItem.personSpecs" placeholder="请填写规格描述" :maxlength="500" allow-clear />
            </h-form-item>
          </h-col>
        </h-row>
      </div>
    </h-form>

    <div class="plan_btns">
      <h-button class="plan_add_btn" type="primary" @click="goodAdd">
        <template #icon>
          <div class="plan_add_img mr8"></div>
        </template>
        <span>添加新需求</span>
      </h-button>
    </div>

    <!-- 礼品选择弹框 -->
    <GiftSelectionModal
      v-model:visible="showGiftModal"
      :current-gift-index="currentGiftIndex"
      @select-gift="handleSelectGift"
    />
  </div>
</template>

<style scoped lang="less">
.demand_present {
  .choose_tip {
    font-size: 14px;
    font-weight: 400;
    color: #86909c;
  }

  .plan_col_list {
    padding: 20px 24px 0;
    background: #f6f9fc;
    border-radius: 8px;
    border: 1px solid #e5e6e8;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_present.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }

    .flex {
      display: flex;
      justify-content: space-between;
    }
    .gift_text {
      width: calc(100% - 90px);
    }
    .gift_select {
      width: 80px;
      display: flex;
      align-items: flex-end;
      padding-bottom: 24px; /* 与form-item的底部间距对齐 */

      :deep(.ant-select) {
        height: 32px;
      }

      :deep(.ant-select-selector) {
        height: 32px !important;
        padding: 4px 11px;
        display: flex;
        align-items: center;
      }
    }
  }

  .plan_btns {
    user-select: none;

    .plan_add_btn {
      padding: 0 15px;
      width: 132px;
      height: 36px;
      box-shadow: 0px 2px 8px 0px rgba(0, 103, 216, 0.1);
      border-radius: 4px;
      display: flex;
      align-items: center;

      font-weight: 500;
      font-size: 14px;
      color: #fff;

      .plan_add_img {
        width: 20px;
        height: 20px;
        background: url('@/assets/image/demand/demand_add_white.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
