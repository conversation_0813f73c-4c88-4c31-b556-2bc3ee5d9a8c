<template>
  <div class="hotel-list-container">
    <h-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      row-key="id"
      @change="handleTableChange"
      :scroll="{ x: 1000 }"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h } from 'vue';
import { useRouter } from 'vue-router';
import { Table as hTable, Button as hButton, message } from 'ant-design-vue';
import { pascalCaseApi } from '@haierbusiness-front/apis';
import { type IPascalCase, type IPascalCaseFilter, type IPageResponse, hotelLevelAllConstant } from '@haierbusiness-front/common-libs';

const router = useRouter();
const loading = ref(false);
const dataSource = ref<IPascalCase[]>([]);

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['10', '20', '50', '100'],
});

const hotalStarList = [
  { text: '五星级', value: 50,code:32 },
  { text: '四星级', value: 40,code:16 },
  { text: '三星级', value: 30,code:8 },
  { text: '二星级', value: 20,code:4 },
  { text: '一星级', value: 10,code:2 },
  { text: '暂无星级', value: 0,code:1 },
];

// //查找酒店星级
// const hotelLevel = (text:number | string)=>{
//   hotalStarList
  
// }

const columns = [
  {
    title: '酒店名称',
    width:'250px',
    ellipsis: true,
    dataIndex: 'platformHotelName',
  },
  {
    title: '酒店地址',
    width:'250px',
    ellipsis: true,
    dataIndex: 'platformHotelAddress',
  },
  {
    title: '酒店编码',
    width:'250px',
    dataIndex: 'platformHotelCode',
  },
  {
    title: '酒店星级',
    dataIndex: 'platformHotelStar',
    width:'180px',
    customRender: ({ text }) => hotalStarList.find(item=>item.code == text)?.text || '暂无星级'
  },
  {
    title: '酒店区域',
    width:'200px',
    dataIndex: 'platformHotelArea',
  },
  {
    title: '询价单code',
    width:'250px',
    dataIndex: 'code',
  },

  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    width:'120px',
    customRender: ({ record }: { record: any }) => {
      return h(
        hButton,
        {
          type: 'link',
          onClick: () => {
            // 注意: 此路由路径从另一个项目复制而来，可能需要进行调整
            router.push({
              path: '/mice-merchant/serviceProvider/hotelDetails',
              query: {
                code: record.platformHotelCode,
                inquiryCode: record.code,
              },
            });
          },
        },
        () => '查看',
      );
    },
  },
];

const fetchData = async () => {
  loading.value = true;
  try {
    const params: IPascalCaseFilter = {
      pageNum: pagination.value.current,
      pageSize: pagination.value.pageSize,
    };
    const res: IPageResponse<IPascalCase> = await pascalCaseApi.hotelList(params);
    dataSource.value = res.records || [];
    pagination.value.total = res.total || 0;
  } catch (error) {
    console.error('获取酒店列表失败:', error);
    message.error('获取酒店列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

const handleTableChange = (paginationInfo: any) => {
  pagination.value.current = paginationInfo.current;
  pagination.value.pageSize = paginationInfo.pageSize;
  fetchData();
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="less" scoped>
.hotel-list-container {
  padding: 16px;
}
</style>
