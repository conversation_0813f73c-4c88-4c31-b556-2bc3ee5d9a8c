<script setup lang="ts">
// 方案互动-价格见证性材料
import { message, Upload } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';

const props = defineProps({});

const emit = defineEmits(['schemeFileEmit']);

const uploadLoading = ref<boolean>(false);
const isLt50M = ref<boolean>(true);
const attachmentList = ref<array>([]);

// 上传价格见证性材料 - 删除
const handleRemove: UploadProps['onRemove'] = (file) => {};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const serviceFileTempSave = () => {
  let paths = [];

  attachmentList.value.forEach((e) => {
    const params = {
      name: e.name,
      url: e.filePath,
    };
    paths.push(JSON.stringify(params));
  });

  emit('schemeFileEmit', [...paths]);
};

// 校验
const serviceFileSub = () => {
  let isVerPassed = true;

  if (!attachmentList.value || attachmentList.value.length === 0) {
    message.error('请上传价格见证性材料！');

    isVerPassed = false;
    anchorJump('schemeAttachmentId');
    return;
  }

  if (isVerPassed) {
    serviceFileTempSave();
  }

  return isVerPassed;
};

defineExpose({ serviceFileSub, serviceFileTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 价格见证性材料 -->
  <div class="scheme_file">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>价格见证性材料</span>
    </div>

    <div class="mt16" id="schemeAttachmentId">
      <a-upload
        v-model:fileList="attachmentList"
        :custom-request="uploadRequest"
        :multiple="false"
        :max-count="10"
        :before-upload="beforeUpload"
        @remove="handleRemove"
      >
        <!-- accept=".rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx" -->
        <a-button>
          <upload-outlined></upload-outlined>
          上传
        </a-button>
      </a-upload>

      <div :class="['support_extend_tip', 'mt18', isLt50M ? '' : 'err_color']">
        <!-- 支持扩展名：.rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx -->
        文件最大不超过50M
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_file {
}
</style>
