<script setup lang="ts">
import { Dropdown as hDropdown, Menu as hMenu, MenuItem as hMenuItem, SubMenu as hSubMenu, Popover as hPopover, message } from 'ant-design-vue';
import { CaretDownFilled, PhoneFilled } from '@ant-design/icons-vue';
import { storeToRefs } from 'pinia';
import { onMounted, ref, computed, watch, onUnmounted } from 'vue';
import { throttle } from 'lodash';
import user from '@/assets/image/icon/user.png';
import logo from '@/assets/image/logo.png'
import logoWhite from '@/assets/image/logo-white.png'
import qrcode from '@/assets/image/qrcode/qrcode.jpg'
import hot from '@/assets/image/icon/hot.png'
import { useRouter } from "vue-router";
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from "@haierbusiness-front/utils/src/store/store"
import { loginApi, indirectApi, processOrchestrationApi } from '@haierbusiness-front/apis';
import { removeStorageItem } from '@haierbusiness-front/utils';
import { useRequest } from 'vue-request';
import {
  HeaderConstant
} from '@haierbusiness-front/common-libs'
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'
const props = defineProps({
  theme: {
    type: String,
    default: 'default',
  }
})
const userList = ['01506579', '01306872', '22069502', '01347713', '01062839', '01435540', '01300132', '00575927', '01424673', '00594769', '19004080', '01377697', '24032740', '20112584', '22023471']
const showTabs = (val?: string) => {
  if (!val) {
    return false
  }
  return userList.indexOf(val) > -1
}

const theme = ref<'default' | 'primary'>('default')
const realTheme = computed(() => (props.theme && props.theme !== 'default') ? props.theme : theme.value)

const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)

const indexUrl = import.meta.env.VITE_BUSINESS_INDEX_URL

const teamUrl = import.meta.env.VITE_BUSINESS_TEAM_URL

const helperUrl = import.meta.env.VITE_BUSINESS_HELPER_URL

const rechargeUrl = import.meta.env.VITE_BUSINESS_RECHARGE_URL

const miceUrl = import.meta.env.VITE_MICE_BID_URL

const { loginUser } = storeToRefs(applicationStore(globalPinia))

const router = useRouter()

// 获取当前用户的可用会展流程
const {
  data: processData,
  run: processListApiRun,
  loading: processLoading,
} = useRequest(processOrchestrationApi.listForUser, {
  manual: false
})

const processUseData = computed(() => {
  if (processData.value) {
    return processData.value.filter(item => item.pdProductLineId != null)
  }
  return []
})

const gotoDetail = (id: number | undefined) => {
  if (!id) {
    message.error('未找到对应的资讯！')
    return
  }
  const thisUrl = router.resolve({
    path: '/travel/detail',
  })
  window.open(thisUrl.href + '?id=' + id + '&type=1', '_blank')
}

const gotoExternalUrl = (url: string | undefined) => {
  if (!url) {
    message.error('链接错误！')
    return
  }
  window.open(url, '_blank')
}

const gotoUrl = (url: string | undefined) => {
  if (!url) {
    message.error('链接错误！')
    return
  }

  window.open(indexUrl + '#' + url, '_blank')
  // router.push({ path: url })
}

const thisPage = (url: string | undefined) => {
  window.location.href = indexUrl + '#' + url
}

const goToTeam = (url: string | undefined) => {
  window.location.href = teamUrl + '#' + url
}
const goToHelper = (url: string | undefined) => {
  window.location.href = helperUrl + '#' + url
}
const goToAccountRecharge = (url: string | undefined) => {
  window.location.href = indexUrl + '#' + url
}
const goToHandover = (url: string | undefined) => {
  window.location.href = indexUrl + '#' + url
}

const openNewWindow = (url: string) => {
  window.open(url, '_blank')
}

const logout = () => {
  loginApi.haierIamTokenLogout({ token: loginUser?.value?.extended?.iamToken }).finally(() => {
    removeStorageItem(HeaderConstant.TOKEN_KEY.key, false)
    window.location.reload()
  })
}

const currentIndex = ref(['1'])
const offset = ref<[number, number]>([-40, 0])

const gotoWater = () => {
  indirectApi.support().then((data) => {
    const url = 'http://gopurchase.haier.com/GoPurchase/login_GL.aspx?PSTRGL=' + data
    window.open(url, '_blank')
  })
}

const goToAnnualBudget = (url: string | undefined) => {
  if (!url) {
    message.error('链接错误！')
    return
  }
  // 确保URL带有#/前缀
  const formattedUrl = url.startsWith('#/') ? url : '#/' + url
  window.open(miceUrl + formattedUrl, '_blank')
}

watch(router.currentRoute, (newValue) => {
  if (newValue.path === '/discount') {
    currentIndex.value = ['11']
  } else if (newValue.path === '/index') {
    currentIndex.value = ['1']
  }
}, { immediate: true, deep: true })
</script>

<template>
  <div :class="['header', `theme-${realTheme}`]">
    <div class="menu flex">
      <div class="logo-con">
        <img :src="realTheme === 'default' ? logoWhite : logo" class="logo">
      </div>
      <div class="menu-list" @mouseenter="theme = 'primary'" @mouseleave="theme = 'default'">
        <h-menu v-model:selectedKeys="currentIndex" mode="horizontal" class="menu-center">
          <h-menu-item key="1" @click="thisPage('/index')">
            <span class="title">首页</span>
          </h-menu-item>
          <h-sub-menu key="2" :popup-offset="offset" :popup-class-name="'global-navigator-menu-submenu'">
            <template #title><span class="title">差旅服务</span></template>
            <h-menu-item key="2-1" @click="openNewWindow('https://travelservice.haier.net/fcc')"><span
                class="sub-title">出差申请</span></h-menu-item>
            <h-menu-item key="2-10" @click="goToTeam('/pc/addTeam')"><span class="sub-title">团队票</span></h-menu-item>
            <h-menu-item key="2-2" @click="openNewWindow('https://travelservice.haier.net/fcc')"><span
                class="sub-title">国内机票</span></h-menu-item>
            <h-menu-item key="2-3" @click="openNewWindow('https://travelservice.haier.net/fcc')"><span
                class="sub-title">国际机票</span></h-menu-item>
            <h-menu-item key="2-4" @click="openNewWindow('https://travelservice.haier.net/fcc')"><span
                class="sub-title">酒店</span></h-menu-item>
            <h-menu-item key="2-8" @click="openNewWindow('https://travelservice.haier.net/fcc')"><span
                class="sub-title">火车</span></h-menu-item>
            <h-menu-item key="2-9" @click="openNewWindow('https://travelservice.haier.net/fcc')"><span
                class="sub-title">地面服务</span></h-menu-item>
          </h-sub-menu>
          <h-sub-menu key="7" :popup-offset="offset" :popup-class-name="'global-navigator-menu-submenu'">
            <template #title><span class="title">青岛餐房</span></template>
            <h-menu-item key="7-1"
              @click="openNewWindow('https://businesstravel.haier.net/localrest/#/?hb-token=' + token)"><span
                class="sub-title">青岛订餐</span></h-menu-item>
            <h-menu-item key="7-2"
              @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/?hb-token=' + token)"><span
                class="sub-title">青岛订房</span></h-menu-item>
          </h-sub-menu>
          <h-sub-menu key="8" :popup-offset="offset" :popup-class-name="'global-navigator-menu-submenu'">
            <template #title><span class="title">商务会展</span></template>
            <h-menu-item v-for="(item, index) in processUseData" :key="`${'8-' + (index + 3)}`" @click="goToAnnualBudget(`${'index?processId=' + item.id }`)"><span
                class="sub-title">{{ item.name }}</span></h-menu-item>
            <h-menu-item key="8-1" @click="goToAnnualBudget('annualMeetingBudget')"><span
                class="sub-title">年度预算</span></h-menu-item>
            <h-menu-item key="8-2" @click="goToAnnualBudget('meetingHandover')"><span
                class="sub-title">会议交接</span></h-menu-item>
          </h-sub-menu>
          <h-sub-menu key="9" :popup-offset="offset" :popup-class-name="'global-navigator-menu-submenu'">
            <template #title><span class="title">企业福利</span></template>
            <h-menu-item key="9-1" @click="openNewWindow('https://cksupermarket.haier.net/')"><span
                class="sub-title">园区福利</span></h-menu-item>
            <h-menu-item key="9-2" @click="openNewWindow('https://businesstravel.haier.net/mall/#/jd/index')"><span
                class="sub-title">京东云超市</span></h-menu-item>
            <h-menu-item key="9-3" @click="openNewWindow('https://netecloud.haier.net/#/home')"><span
                class="sub-title">网易云超市</span></h-menu-item>

            <template v-if="showTabs(loginUser?.username)">
              <h-sub-menu key="9-6" :popup-class-name="'global-navigator-menu-submenu'">
                <template #title><span class="title">达产激励</span></template>
                <h-menu-item key="9-6-1" @click="goToAccountRecharge('/recharge/accountRecharge')"><span
                    class="sub-title">工会账户充值</span></h-menu-item>
                <h-menu-item key="9-6-2" @click="goToAccountRecharge('/excitation/addExcitation')"><span
                    class="sub-title">工会激励下发</span></h-menu-item>
              </h-sub-menu>
            </template>
          </h-sub-menu>
          <h-menu-item key="11">
            <div class="title gradient discount" @click="thisPage('/discount')">
              <div class="user-discount">
                <span style="--i:1" class="gradient-1">员</span>
                <span style="--i:2" class="gradient-2">工</span>
                <span style="--i:3" class="gradient-3">特</span>
                <span style="--i:4" class="gradient-4">惠</span>
              </div>
              <div class="hot">
                <img :src="hot" class="hot-img" style="--i:5" />
              </div>
            </div>
          </h-menu-item>
          <h-menu-item key="12">
            <span class="title" @click="goToHelper('/helper')">创客帮</span>
          </h-menu-item>
          <h-menu-item key="13">
            <span class="title" @click="openNewWindow('https://businesstravel.haier.net/#/about')">关于我们</span>
          </h-menu-item>
        </h-menu>
      </div>
    </div>
    <div class="headerCon">
      <div>
        <h-dropdown overlayClassName="global-custom-dropdown">
          <div class="user">
            <div class="hc">
              <img :src="user" class="icon">

            </div>
            <span>{{ loginUser?.nickName }}</span>
            <div>
              <CaretDownFilled class="font-10" />
            </div>
          </div>
          <template #overlay>
            <h-menu>
              <h-menu-item @click="logout">
                退出登录
              </h-menu-item>
            </h-menu>
          </template>
        </h-dropdown>
      </div>

      <div class="mr-20 flex">
        <h-dropdown class="header-dropdown" overlayClassName="global-custom-dropdown">
          <div class="flex order">
            <div class="hc">
              <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink">
                <g id="首页-需求提报" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g id="top-icon-20250408" transform="translate(-324.1, -35.6154)">
                    <g id="编组-2" transform="translate(90, 28)">
                      <g id="订单中心" transform="translate(0, 5)">
                        <g id="编组" transform="translate(234.1, 0)">
                          <g id="订单管理" transform="translate(0, 2.6154)">
                            <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="16" height="16"></rect>
                            <path
                              d="M13.853125,3.89399038 L10.490625,0.531490385 C10.396875,0.437740385 10.2703125,0.384615385 10.1375,0.384615385 L3.5,0.384615385 C3.2234375,0.384615385 3,0.608052885 3,0.884615385 L3,13.8846154 C3,14.1611779 3.2234375,14.3846154 3.5,14.3846154 L13.5,14.3846154 C13.7765625,14.3846154 14,14.1611779 14,13.8846154 L14,4.24867788 C14,4.11586538 13.946875,3.98774038 13.853125,3.89399038 Z M12.846875,4.47836538 L9.90625,4.47836538 L9.90625,1.53774038 L12.846875,4.47836538 Z M12.875,13.2596154 L4.125,13.2596154 L4.125,1.50961538 L8.84375,1.50961538 L8.84375,4.88461538 C8.84375,5.24711538 9.1375,5.54086538 9.5,5.54086538 L12.875,5.54086538 L12.875,13.2596154 Z"
                              id="形状" fill="currentColor" fill-rule="nonzero"></path>
                            <path
                              d="M8.375,9.04086538 L5.5,9.04086538 C5.43125,9.04086538 5.375,9.09711538 5.375,9.16586538 L5.375,9.91586538 C5.375,9.98461538 5.43125,10.0408654 5.5,10.0408654 L8.375,10.0408654 C8.44375,10.0408654 8.5,9.98461538 8.5,9.91586538 L8.5,9.16586538 C8.5,9.09711538 8.44375,9.04086538 8.375,9.04086538 Z M5.375,7.04086538 L5.375,7.79086538 C5.375,7.85961538 5.43125,7.91586538 5.5,7.91586538 L11.5,7.91586538 C11.56875,7.91586538 11.625,7.85961538 11.625,7.79086538 L11.625,7.04086538 C11.625,6.97211538 11.56875,6.91586538 11.5,6.91586538 L5.5,6.91586538 C5.43125,6.91586538 5.375,6.97211538 5.375,7.04086538 Z"
                              id="形状" fill="currentColor" fill-rule="nonzero"></path>
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
              <span class="pl-8 font-14" :style="{ marginLeft: '5px' }">订单中心</span>
            </div>
            <div class="flex">
              <CaretDownFilled class="font-10" />
            </div>
          </div>
          <template #overlay>
            <h-menu class="order-menu">
              <h-menu-item class="order-menu" @click="gotoUrl('/card-order/localrest')">
                <span class="order-type">
                  订餐
                </span>
              </h-menu-item>
              <h-menu-item class="order-menu" @click="gotoUrl('/card-order/localhotel')">
                <div class="order-type">
                  订房
                </div>
              </h-menu-item>
              <!-- <h-menu-item class="order-menu">
                        <div class="order-type">
                        网易云
                        </div>
                    </h-menu-item> -->
              <h-menu-item class="order-menu" @click="gotoUrl('/card-order/jd')">
                <div class="order-type">
                  京东
                </div>
              </h-menu-item>
              <h-menu-item class="order-menu" @click="gotoUrl('/card-order/mice')">
                <div class="order-type">
                  会展
                </div>
              </h-menu-item>
              <h-menu-item class="order-menu"
                @click="gotoExternalUrl('https://travelservice.haier.net/fcc/ddgl/ddgl.html?cplx=jd')">
                <div class="order-type">
                  酒店
                </div>
              </h-menu-item>
              <h-menu-item class="order-menu"
                @click="gotoExternalUrl('https://travelservice.haier.net/fcc/ddgl/ddgl.html?cplx=hcp')">
                <div class="order-type">
                  火车票
                </div>
              </h-menu-item>
              <h-menu-item class="order-menu"
                @click="gotoExternalUrl('https://travelservice.haier.net/fcc/ddgl/ddgl.html?cplx=yc')">
                <div class="order-type">
                  用车
                </div>
              </h-menu-item>
              <h-menu-item class="order-menu"
                @click="gotoExternalUrl('https://travelservice.haier.net/fcc/ddgl/ddgl.html?cplx=jp')">
                <div class="order-type">
                  机票
                </div>
              </h-menu-item>

              <h-menu-item class="order-menu" @click="thisPage('/card-order/team')">
                <div class="order-type">
                  团队票
                </div>
              </h-menu-item>

              <template v-if="showTabs(loginUser?.username)">
                <h-sub-menu :popup-class-name="'order-menu'">
                  <template #title><span style="">达产激励</span></template>
                  <h-menu-item class="order-menu" @click="thisPage('/card-order/recharge')">
                    <div class="order-type">
                      达产充值订单
                    </div>
                  </h-menu-item>
                  <h-menu-item class="order-menu" @click="thisPage('/card-order/excitation')">
                    <div class="order-type">
                      激励下发订单
                    </div>
                  </h-menu-item>
                </h-sub-menu>
              </template>


            </h-menu>
          </template>
        </h-dropdown>
      </div>

      <div class="mr-20 flex">
        <h-dropdown class="header-dropdown" overlayClassName="global-custom-dropdown">
          <div class="flex order">
            <div class="hc">
              <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink">
                <g id="UI-首页/需求提报" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g id="用户端_异地模式-用户（经办人）-首页4-20250321" transform="translate(-1464.1, -37)">
                    <g id="编组-2" transform="translate(1350, 30)">
                      <g id="订单中心" transform="translate(0, 5)">
                        <g id="编组备份" transform="translate(114.1, 0)">
                          <g id="审批" transform="translate(0, 2)">
                            <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="16" height="16"></rect>
                            <path
                              d="M11.35,1 C11.7876072,1 12.2072912,1.18437271 12.5167262,1.51255825 C12.8261612,1.84074379 13,2.2858589 13,2.74998334 L13,14.416539 C13,14.6139972 12.9058602,14.7980639 12.7497666,14.9056846 C12.5936731,15.0133053 12.3968418,15.0298686 12.2267,14.9497006 L7.5,12.7213885 L2.77330001,14.9497006 C2.60973468,15.026821 2.42101547,15.0146969 2.267423,14.9172009 C2.11383054,14.8197049 2.01458038,14.6490345 2.001375,14.4597052 L2,14.416539 L2,2.74998334 C2,2.2858589 2.17383879,1.84074379 2.48327381,1.51255825 C2.79270884,1.18437271 3.2123928,1 3.65000001,1 L11.35,1 Z M11.35,2.16665557 L3.65000001,2.16665557 C3.3462434,2.16665557 3.1,2.42782031 3.1,2.74998334 L3.1,13.5187975 L7.27669999,11.5500663 C7.40258434,11.4907251 7.54488186,11.4836475 7.67544999,11.5302331 L7.7233,11.5500663 L11.9,13.5187975 L11.9,2.74998334 C11.9000911,2.44472069 11.6782612,2.19100857 11.39125,2.16811388 L11.35,2.16665557 Z M10.63885,4.67088173 C10.8406308,4.88486627 10.8546034,5.22697714 10.671025,5.4586659 L10.63885,5.49570722 L7.06385,9.2873378 C6.86209123,9.50134567 6.53952648,9.51616498 6.32107499,9.32146247 L6.28615,9.2873378 L4.36115,7.24569056 C4.15333987,7.02473725 4.14631383,6.6694496 4.34520607,6.43944923 C4.54409831,6.20944886 4.87844418,6.18622158 5.103925,6.3867404 L5.13885,6.42086509 L6.67499999,8.04951625 L9.86115,4.67059007 C10.0759249,4.44286945 10.4240751,4.44286945 10.63885,4.67059007 L10.63885,4.67088173 Z"
                              id="形状" fill="currentColor" fill-rule="nonzero"></path>
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
              <span class="pl-8 font-14" :style="{ marginLeft: '5px' }">我的审批</span>
            </div>
            <div class="flex">
              <CaretDownFilled class="font-10" />
            </div>
          </div>
          <template #overlay>
            <h-menu class="order-menu">
              <h-menu-item class="order-menu" @click="gotoUrl('/portal-control/me')">
                <span class="order-type">
                  我发起的
                </span>
              </h-menu-item>
              <h-menu-item class="order-menu" @click="gotoUrl('/portal-control/todo')">
                <div class="order-type">
                  待我审批
                </div>
              </h-menu-item>
            </h-menu>
          </template>
        </h-dropdown>
      </div>

      <div class="mr-24">
        <a-divider class="divider" type="vertical" />
      </div>

      <div class="mr-28 flex">
        <h-popover placement="bottom">
          <template #content>
            <div class="contact">
              <div class="phone">
                <PhoneFilled />
              </div>
              <div class="number">
                0532-88931999
              </div>
            </div>
            <div class="contact">
              <div class="phone">

              </div>
              <div class="number">
                (或)4006-999-521
              </div>
            </div>

          </template>
          <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="UI-首页/需求提报" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g id="用户端_异地模式-用户（经办人）-首页4-20250321" transform="translate(-1394, -37)" fill="currentColor">
                <g id="编组-2" transform="translate(1350, 30)">
                  <g id="订单中心" transform="translate(0, 5)">
                    <g id="编组备份" transform="translate(44, 2)">
                      <rect id="矩形" fill-opacity="0" x="0" y="0" width="16" height="16"></rect>
                      <path
                        d="M7.93333333,0.666666667 C10.7927435,0.666666667 13.1265806,2.91691255 13.2605904,5.74315013 C14.2614643,6.00545768 15,6.91648171 15,8 L15,9.33333333 C15,10.5157564 14.1204819,11.4927569 12.979967,11.6458139 C12.278766,13.3658324 10.9209667,14.4728981 9.10101423,14.6435741 C8.87433037,15.05477 8.43636283,15.3333333 7.93333333,15.3333333 C7.19695367,15.3333333 6.6,14.7363797 6.6,14 C6.6,13.2636203 7.19695367,12.6666667 7.93333333,12.6666667 C8.41504837,12.6666667 8.83709827,12.9221235 9.07140266,13.3049567 C10.2242214,13.1652396 11.0973462,12.5017536 11.6226307,11.4205623 C10.8580862,11.0374315 10.3333333,10.2466637 10.3333333,9.33333333 L10.3333333,8 C10.3333333,6.96970886 11.0010911,6.09538089 11.9274252,5.78619743 C11.8168291,3.67667653 10.0708856,2 7.93333333,2 C5.80916828,2 4.07172219,3.65574065 3.94120415,5.74701283 C4.93533456,6.01436959 5.66666667,6.92177891 5.66666667,8 L5.66666667,9.33333333 C5.66666667,10.6219977 4.62199775,11.6666667 3.33333333,11.6666667 C2.04466892,11.6666667 1,10.6219977 1,9.33333333 L1,8 C1,6.96589114 1.67271563,6.08890344 2.60440006,5.78278371 C2.71805315,2.9382178 5.06041865,0.666666667 7.93333333,0.666666667 Z M3.33333333,7 C2.80077304,7 2.36544637,7.41630665 2.3350309,7.9412424 L2.33333333,8 L2.33333333,9.33333333 C2.33333333,9.88561808 2.78104858,10.3333333 3.33333333,10.3333333 C3.86589363,10.3333333 4.30122029,9.91702668 4.33163577,9.39209093 L4.33333333,9.33333333 L4.33333333,8 C4.33333333,7.44771525 3.88561808,7 3.33333333,7 Z M12.6666667,7 C12.1341064,7 11.6987797,7.41630665 11.6683642,7.9412424 L11.6666667,8 L11.6666667,9.33333333 C11.6666667,9.88561808 12.1143819,10.3333333 12.6666667,10.3333333 C13.199227,10.3333333 13.6345536,9.91702668 13.6649691,9.39209093 L13.6666667,9.33333333 L13.6666667,8 C13.6666667,7.44771525 13.2189514,7 12.6666667,7 Z"
                        id="形状" fill-rule="nonzero"></path>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </h-popover>
      </div>

      <div class="mr-28 flex">
        <h-popover placement="bottom">
          <template #content>
            <img :src="qrcode" class="qrcode pointer">
          </template>
          <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="UI-首页/需求提报" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g id="用户端_异地模式-用户（经办人）-首页4-20250321" transform="translate(-1350, -37)" fill="currentColor">
                <g id="编组-2" transform="translate(1350, 30)">
                  <g id="订单中心" transform="translate(0, 5)">
                    <g id="微信" transform="translate(0, 2)">
                      <polygon id="矩形" fill-opacity="0" points="16 0 16 16 0 16 0 0"></polygon>
                      <path
                        d="M8.00005944,0.666725852 C12.0501438,0.666725852 15.3334517,3.94996989 15.3334517,8.00005906 C15.3334517,9.77576347 14.7022692,11.4040535 13.6521023,12.6728643 C14.6319275,13.6372739 15.1218402,14.1194787 15.1218402,14.1194787 C15.3970613,14.3903128 15.4047467,14.8372639 15.1390296,15.117778 C15.0084957,15.2555711 14.828623,15.3333629 14.6406982,15.3333629 L8.00005944,15.3333629 C3.99046993,15.3333629 0.732462118,12.1154881 0.667708523,8.12132281 L0.666725852,8.00005906 C0.666725852,3.94996989 3.9499751,0.666725852 8.00005944,0.666725852 Z M8.000048,2 C4.72316,2 2.05982,4.626932 2.000996,7.879328 L2,7.9892 L2.000639,8.072045 C2.000781,8.090455 2.000852,8.09966 2.000852,8.09966 C2.053292,11.333756 4.663052,13.940168 7.868756,13.999136 L8.026064,14 L13.099712,14 L11.844992,12.764984 C12.36496,12.136768 12.624944,11.82266 12.624944,11.82266 C13.50914,10.754372 14,9.416696 14,8 C14,4.686296 11.313752,2 8.000048,2 Z"
                        id="差集"></path>
                      <path
                        d="M5.33327415,9 C4.78099015,9 4.33327415,8.552284 4.33327415,8 C4.33327415,7.447716 4.78099015,7 5.33327415,7 C5.88555815,7 6.33327415,7.447716 6.33327415,8 C6.33327415,8.552284 5.88555815,9 5.33327415,9 C5.33327415,9 5.33327415,9 5.33327415,9 Z"
                        id="形状"></path>
                      <path
                        d="M9.6665483,8 C9.6665483,8.552284 10.1142643,9 10.6665483,9 C11.2188323,9 11.6665483,8.552284 11.6665483,8 C11.6665483,7.447716 11.2188323,7 10.6665483,7 C10.1142643,7 9.6665483,7.447716 9.6665483,8 C9.6665483,8 9.6665483,8 9.6665483,8 Z"
                        id="形状"></path>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </h-popover>
      </div>

      <!-- <div class="mr-26">
            <img :src="notice" class="icon pointer">
        </div> -->

    </div>
  </div>
</template>

<style scoped lang="less">
.pointer {
  cursor: pointer;
}

.flex {
  display: flex;
}

.mr-5 {
  margin-right: 5px;
}

.mr-7 {
  margin-right: 7px;
}

.mr-13 {
  margin-right: 13px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-24 {
  margin-right: 24px;
}

.mr-26 {
  margin-right: 26px;
}

.mr-28 {
  margin-right: 28px;
}

.mr-32 {
  margin-right: 32px;
}

.font-10 {
  font-size: 10px;
}

.font-14 {
  font-size: 14px;
}

.icon {
  width: 22px;
  height: 22px;
}

.vertical {
  width: 1px;
}

.header {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 78px;
  padding-right: 88px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .menu {
    height: 100%;
    flex: 1;
    display: flex;

    .logo-con {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 250px;

      .logo {
        width: 153px;
      }
    }

    .menu-list {
      width: 0;
      flex: 1;

      :deep(.ant-menu) {
        border-bottom: none;
      }

      :deep(.ant-menu-horizontal) {
        line-height: 77px;
      }

      .menu-center {
        background-color: transparent;
        display: flex;
        width: 100%;
        height: 77px;
      }

      .title {
        font-size: 16px;
        font-weight: 500;
      }

      .gradient-1 {
        background: linear-gradient(to right, #FF8D2E 0%, #FF8D2E 50%);
        -webkit-background-clip: text;
        /*将设置的背景颜色限制在文字中*/
        -webkit-text-fill-color: transparent;
        /*给文字设置成透明*/
      }

      .gradient-2 {
        background: linear-gradient(to right, #FF8D2E 50%, #FF0D0F 100%);
        -webkit-background-clip: text;
        /*将设置的背景颜色限制在文字中*/
        -webkit-text-fill-color: transparent;
        /*给文字设置成透明*/
      }

      .gradient-3 {
        background: linear-gradient(to right, #FF0D0F 0%, #FF0D0F 50%);
        -webkit-background-clip: text;
        /*将设置的背景颜色限制在文字中*/
        -webkit-text-fill-color: transparent;
        /*给文字设置成透明*/
      }

      .gradient-4 {
        background: linear-gradient(to right, #FF0D0F 50%, #FF0D0F 100%);
        -webkit-background-clip: text;
        /*将设置的背景颜色限制在文字中*/
        -webkit-text-fill-color: transparent;
        /*给文字设置成透明*/
      }

      .discount {
        display: flex;
        flex-direction: row;
        align-items: center;
        position: relative;

        .user-discount {
          display: flex;

          span {
            /* 设置行内块元素 */
            display: inline-block;
            /* 添加动画 */
            animation: jump 1.5s ease-in-out infinite;
            /* 利用变量动态计算动画延迟时间 */
            animation-delay: calc(.1s*var(--i));
          }
        }

        .hot {
          position: absolute;
          width: 28px;
          top: -15px;
          right: -28px;
          /* 添加动画 */
          animation: jump 1.5s ease-in-out infinite;
          /* 利用变量动态计算动画延迟时间 */
          animation-delay: .5s;

          .hot-img {
            width: 28px;
          }
        }
      }

    }
  }

  .headerCon {
    display: flex;
    height: 40px;
    flex-direction: row-reverse;
    align-items: center;

    .user {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      width: 130px;
      border-radius: 16px;
      padding-left: 12px;
      padding-right: 12px;
      cursor: pointer;
      font-family: Microsoft YaHei;
    }

    .order {
      align-items: center;
      justify-content: space-between;
      height: 32px;
      width: 105px;
      border-radius: 16px;

      cursor: pointer;
    }
  }
}

.theme-default {
  background-color: transparent;
  color: #fff;

  .menu {
    .menu-list {

      .title,
      :deep(.anticon) {
        color: #fff;
      }
    }
  }

  .divider {
    background-color: #fff;
  }
}

.theme-primary {
  background-color: #fff;
  border-bottom: 1px solid #E5E6EB;
  color: #595959;

  .menu {
    .menu-list {

      .title,
      :deep(.anticon) {
        color: #595959;
      }
    }
  }

  .divider {
    background-color: #595959;
  }
}

.order-type {
  // color: #3983E5;
}

.contact {
  display: flex;
  flex-direction: row;

  .phone {
    display: flex;
    width: 26px;
    align-items: center;
  }

  .number {
    display: flex;
  }
}

.qrcode {
  width: 150px;
  height: 150px;
}
</style>

<style lang="less">
.global-navigator-menu-submenu {
  border-radius: 8px;

  >.ant-menu {
    border-radius: 8px;

    >.ant-menu-item {
      background-color: transparent !important;
      text-align: center;

      &:hover {
        .sub-title {
          color: #1868DB;
        }
      }
    }
  }
}

.global-custom-dropdown {
  border-radius: 8px;

  >.ant-dropdown-menu {
    border-radius: 8px;

    >.ant-dropdown-menu-item {
      background-color: transparent !important;
      text-align: center;

      &:hover {
        .ant-dropdown-menu-title-content {
          color: #1868DB;
        }
      }
    }
  }
}

.ant-menu-submenu-popup {
  border-radius: 8px;

  >.ant-menu {
    border-radius: 8px;

    >.ant-menu-submenu {
      >.ant-menu-submenu-title {
        background-color: transparent !important;
        text-align: center;
      }
    }

    >.ant-menu-item {
      background-color: transparent !important;
      text-align: center;

      &:hover {
        .title {
          color: #1868DB;
        }
      }
    }
  }
}
</style>

<style>
@keyframes jump {
  0 {
    transform: translateY(0px);
  }

  20% {
    transform: translateY(-7px);
  }

  40%,
  100% {
    transform: translateY(0px);
  }
}
</style>