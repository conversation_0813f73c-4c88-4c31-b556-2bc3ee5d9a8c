import { IPageRequest } from "../../basic";

export class IAnnouncementNoticeFilter extends IPageRequest {
    begin?:string
    end?:string
    isWindowShow?:boolean
    effectScope?:number
}

export class IAnnouncementNotice {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    title?: string
    effectScope?: number
    contentForm?: number
    informContent?: string
    sort?: number
    isWindow?: boolean
    name?: string
    code?: string
    state?: string
    description?: string
}