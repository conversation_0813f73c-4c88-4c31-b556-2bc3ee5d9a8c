<!-- 服务商考核管理端 -->

<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  message,
  Modal,
} from 'ant-design-vue';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { serviceExamApi, fileApi } from '@haierbusiness-front/apis';
import {
  ServiceExamFilter,
  ExamineStateEnum,
  ExamineStateMap,
  PunishmentStatusEnum,
  ViolationTypeMap,
  ViolationTypeEnum,
  getPunishmentStatusDesc,
  PunishmentStatusMap,
  AssessmentStatusMap
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, reactive, h, nextTick } from 'vue';
import { usePagination, } from 'vue-request';
import router from '../../router'
import ColumnFilter from '@haierbusiness-front/components/mice/search/ColumnFilter.vue';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';

const currentRouter = ref()
const violationDetailsVisible = ref(false)
const currentRecord = ref<any>(null)
const processingRecords = ref<any[]>([])
const processingPagination = ref({
  current: 1,
  pageSize: 3,
  total: 0,
  showSizeChanger: false
})

onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })
})

const columns: ColumnType[] = [
  {
    title: '考核单号',
    dataIndex: 'examineCode',
    width: '300px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '260px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '关联单号',
    dataIndex: 'mainCode',
    width: '260px',
    align: 'center',
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '考核明细',
    dataIndex: 'details',
    width: '200px',
    align: 'center',
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '考核条目',
    dataIndex: 'entry',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '违规类型',
    dataIndex: 'type',
    width: '140px',
    align: 'center',
    customRender: ({ text }) => {
      const content = text !== undefined && text !== null ? ViolationTypeMap[text as ViolationTypeEnum] || '-' : '-';
      return {
        children: content,
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '违规措施',
    dataIndex: 'score',
    width: '160px',
    align: 'center',
    customRender: ({ record }) => {
      let text = '';
      if (record.score) {
        if (record.score < 0) {
          text += `扣${Math.abs(record.score)}分`;
        } else if (record.score > 0) {
          text += `加${record.score}分`;
        }
      }
      if (record.fine) {
        if (text) text += '，';
        if (record.fine < 0) {
          text += `罚款${Math.abs(record.fine)}元`;
        } else if (record.fine > 0) {
          text += `奖励${record.fine}元`;
        }
      }
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '违规时间',
    dataIndex: 'violationTime',
    width: '180px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },

  },
  {
    title: '处理截止时间',
    dataIndex: 'violationDisposeEndTime',
    width: '200px',
    align: 'center',
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },

  },
  {
    title: '考核状态',
    dataIndex: 'examineState',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => {
      const content = text !== undefined && text !== null ? ExamineStateMap[text as ExamineStateEnum] || '-' : '-';
      return {
        children: content,
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '违规状态',
    dataIndex: 'state',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => {
      const content = text !== undefined && text !== null ? getPunishmentStatusDesc(text) || '-' : '-';
      return {
        children: content,
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '130px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return {
        children: text || '-',
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];

const searchParam = ref<ServiceExamFilter>({
  examineCode: '',
  examineState: undefined,
  type: undefined,
  mainCode: '',
  entry: "",
  score: undefined,
  state: undefined,
  createName: "",
  violationTime: "",
  violationDisposeEndTime: '',
  violationDisposeStartTime:"",
  merchantName: '',
  gmtCreate: '',
  againTime: undefined,
  endTime: undefined,
  
  gmtCreateEnd: "",
  details: '',
})
const {
  data,
  run: listApiRun,
  loading,
} = usePagination(serviceExamApi.list);

const reset = () => {
  searchParam.value = {}
  beginAndEnd.value = undefined
  gmtCreate.value = undefined
  violationDisposeEndTime.value = undefined
  console.log('重置后的搜索参数:', searchParam.value);

  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {

  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  };


  console.log('最终查询参数:', params);
  listApiRun(params);
};

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: any) => {
  if (n) {
    searchParam.value.againTime = n[0]
    searchParam.value.endTime = n[1]
  } else {
    searchParam.value.againTime = undefined
    searchParam.value.endTime = undefined
  }
});
const gmtCreate = ref<[Dayjs, Dayjs]>()
watch(() => gmtCreate.value, (n: any) => {
  if (n) {
    searchParam.value.gmtCreate = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.gmtCreateEnd = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.gmtCreate = undefined
    searchParam.value.gmtCreateEnd = undefined
  }
});
const violationDisposeEndTime = ref<[Dayjs, Dayjs]>()
watch(() => violationDisposeEndTime.value, (n: any) => {
  if (n) {
    searchParam.value.violationDisposeStartTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.violationDisposeEndTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.violationDisposeStartTime = undefined
    searchParam.value.violationDisposeEndTime = undefined
  }
});

const showViolationDetails = async (record: any, isEdit: boolean) => {
  console.log('showViolationDetails', record, isEdit);

  // 跳转到详情页，传递ID和编辑模式参数
  currentRouter.value.push({
    path: '/bidman/serviceExam/serviceExamDetails',
    query: {
      id: record.id,
      isEdit: isEdit.toString()
    }
  })

}

const handleProcessingTableChange = (pagination: any) => {
  processingPagination.value.current = pagination.current;
  showViolationDetails(currentRecord.value, false);
}

const handleViolationDetailsClose = () => {
  violationDetailsVisible.value = false;
  currentRecord.value = null;
  processingRecords.value = [];
  processingPagination.value.current = 1;
}

const handleViolationDetailsOk = async (formData: { result: string; paymentRecord: string }) => {
  try {
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    });

    violationDetailsVisible.value = false;
    currentRecord.value = null;
  } catch (error) {
    console.error('Failed to process violation:', error);
  }
}

const handleCreate = () => {
  currentRouter.value.push({
    path: '/bidman/serviceExam/list/add',
  })
}

const handleRevoke = async (record: any) => {
  console.log('撤销操作:', record.id);

  if (!record?.id) {
    message.error('无法获取记录ID');
    return;
  }
  serviceExamApi.getAssessmentCode({ id: record.id })
    .then(result => {
      console.log('获取询价单号返回结果:', result);
      // 直接跳转到流程页面
      if (result) {
        const processUrl = `https://businessmanagement-test.haier.net/hbweb/process/?code=${result.code}#/details`;
        window.open(processUrl, '_blank');
      } else {
        message.error('获取流程编码失败');
      }
    })
    .catch(error => {
      console.error('获取询价单号失败:', error);
      message.error('获取询价单号失败');
    });

}

// 添加撤销相关状态
const revokeVisible = ref(false);
const revokeFileList = ref<any[]>([]);
const revokeLoading = ref(false);

// 添加查看取消原因相关状态
const reasonVisible = ref(false);
const reasonLoading = ref(false);
const cancelReason = ref<string>('');
const cancelFiles = ref<string[]>([]);

// 查看取消原因
const handleViewCancelReason = async (record: any) => {
  if (!record?.id) return;

  reasonLoading.value = true;
  try {
    const result: any = await serviceExamApi.getReason({ id: record.id });
    console.log('撤销原因返回数据:', result);

    // 处理返回null的情况
    if (result === null) {
      cancelReason.value = '空';
      cancelFiles.value = [];
      currentRecord.value = record;
      reasonVisible.value = true;
      return;
    }

    // 处理返回字符串的情况
    if (typeof result === 'string') {
      cancelReason.value = result;
      cancelFiles.value = [];
      currentRecord.value = record;
      reasonVisible.value = true;
      return;
    }

    if (result && result.success) {
      // 处理返回的数据格式 {"data":"123","success":true}
      if (typeof result.data === 'string') {
        cancelReason.value = result.data || '无撤销原因';
        cancelFiles.value = [];
      } else if (result.data && typeof result.data === 'object') {
        cancelReason.value = result.data.reason || '无撤销原因';
        cancelFiles.value = result.data.path || [];
      }
      currentRecord.value = record;
      reasonVisible.value = true;
    } else if (result && result.records && result.records.length > 0) {
      // 原来的处理逻辑保留作为备选
      const reasonData = result.records[0];
      cancelReason.value = reasonData.reason || '无撤销原因';
      cancelFiles.value = reasonData.path || [];
      currentRecord.value = record;
      reasonVisible.value = true;
    } else {
      message.error('获取撤销原因失败');
    }
  } catch (error) {
    console.error('获取撤销原因失败:', error);
    message.error('获取撤销原因失败，请重试');
  } finally {
    reasonLoading.value = false;
  }
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';
const uploadRequest = (options: any) => {
  revokeLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi.upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      revokeLoading.value = false;
    });
};

// 移除文件
const handleRevokeFileRemove = (file: any) => {
  const index = revokeFileList.value.indexOf(file);
  if (index !== -1) {
    revokeFileList.value.splice(index, 1);
  }
};

// 执行撤销
const handleConfirmRevoke = () => {
  if (!currentRecord.value?.id) return;

  if (!revokeFileList.value || revokeFileList.value.length === 0) {
    message.error('请上传撤销凭证');
    return;
  }

  revokeLoading.value = true;

  serviceExamApi.cancelExam({
    id: currentRecord.value.id,
    path: revokeFileList.value.map(file => file.filePath),
  })
    .then(() => {
      message.success('撤销成功');
      revokeVisible.value = false;
      // 重置表单
      revokeFileList.value = [];
      // 刷新表格
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('撤销失败:', error);
      message.error('撤销失败，请重试');
    })
    .finally(() => {
      revokeLoading.value = false;
    });
};

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'view':
      showViolationDetails(record, false);
      break;
    case 'process':
      showViolationDetails(record, true);
      break;
    case 'revoke':
      if (record.examineState === ExamineStateEnum.UNPROCESSED) {
        handleRevoke(record);
      }
      break;
    case 'viewReason':
      handleViewCancelReason(record);
      break;
    default:
      break;
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [];

  // 不再添加查看按钮，因为它将单独显示在外面
  if (record.state === 20) {
    options.push({
      key: 'process',
      label: '处理',
    });
  }

  if (record.examineState === 10) {
    options.push({
      key: 'revoke',
      label: '审批查看',
    });
  }

  if (record.examineState === ExamineStateEnum.CANCELLED) {
    options.push({
      key: 'viewReason',
      label: '查看驳回原因',
    });
  }

  return options;
};
//显示更多
const hasMore = ref(false)
const isExpanded = ref(false)
const toggleExpand = () => {
  hasMore.value = !hasMore.value
  isExpanded.value = !isExpanded.value
}



</script>

<template>
  <div class="page-container">
    <h-row class="row-container">
      <h-col :span="24" class="col-margin">
        <h-row class="search-row">
          <h-col :span="2" class="label-right">
            <label for="examineCode">考核单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="examineCode" v-model:value="searchParam.examineCode" placeholder="请输入" allow-clear />
          </h-col>
          <h-col :span="2" class="label-right">
            <label for="mainCode">关联单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="mainCode" v-model:value="searchParam.mainCode" placeholder="请输入关联单号" allow-clear />
          </h-col>
          <h-col :span="2" class="label-right">
            <label for="examineState">考核状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="examineState" v-model:value="searchParam.examineState" placeholder="请选择状态" class="full-width"
              allow-clear>
              <h-select-option v-for="(value, key) in ExamineStateMap" :key="key" :value="Number(key)">
                {{ value }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" class="label-right">
            <label for="type">违规类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="type" v-model:value="searchParam.type" placeholder="请选择类型" class="full-width" allow-clear>
              <h-select-option v-for="(value, key) in ViolationTypeMap" :key="key" :value="Number(key)">
                {{ value }}
              </h-select-option>
            </h-select>
          </h-col>
          <!-- 第二行 -->
          <h-col :span="2" class="label-right margin-top">
            <label for="merchantName">服务商名称：</label>
          </h-col>
          <h-col :span="4" class="margin-top">
            <h-input id="merchantName" v-model:value="searchParam.merchantName" placeholder="请输入" allow-clear />
          </h-col>
          <h-col :span="2" class="label-right margin-top">
            <label for="createTime">违规时间：</label>
          </h-col>
          <h-col :span="4" class="margin-top">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" class="full-width" allow-clear />
          </h-col>
          <h-col :span="2" class="label-right margin-top">
            <label for="createTime">考核条目：</label>
          </h-col>
          <h-col :span="4" class="margin-top">
            <h-input id="merchantName" v-model:value="searchParam.entry" placeholder="请输入考核条目" allow-clear />
          </h-col>

          <h-col :span="2" class="label-right margin-top">
            <label for="merchantName">处理截止时间：</label>
          </h-col>
          <h-col :span="4" class="margin-top">
            <h-range-picker v-model:value="violationDisposeEndTime" value-format="YYYY-MM-DD" class="full-width"
              allow-clear />
          </h-col>
        </h-row>
        <!-- 第三行 -->
        <h-row v-if="hasMore" style="padding: 0px 10px 0px 10px;">
          <h-col :span="2" class="label-right margin-top">
            <label for="createTime">违规状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="type" v-model:value="searchParam.state" placeholder="请选择类型" class="full-width" allow-clear
              style="margin-top: 10px;">
              <h-select-option v-for="(value, key) in PunishmentStatusMap" :key="key" :value="Number(key)">
                {{ value }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" class="label-right margin-top">
            <label for="createTime">创建人：</label>
          </h-col>
          <h-col :span="4" class="margin-top">
            <h-input id="merchantName" v-model:value="searchParam.createName" placeholder="请输入创建人" allow-clear />
          </h-col>
          <h-col :span="2" class="label-right margin-top">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4" class="margin-top">
            <h-range-picker v-model:value="gmtCreate" value-format="YYYY-MM-DD" class="full-width" allow-clear />
          </h-col>
          <h-col :span="2" class="label-right margin-top">
            <label for="createTime">考核明细：</label>
          </h-col>
          <h-col :span="4" class="margin-top">
            <h-input id="merchantName" v-model:value="searchParam.details" placeholder="请输入考核明细" allow-clear />
          </h-col>
        </h-row>
        <h-row class="button-row">
          <h-col :span="24" class="text-right">
            <h-button @click="toggleExpand" type="link">
              <UpOutlined v-if="isExpanded" />
              <DownOutlined v-else />
              高级搜索
              <h-icon :type="isExpanded ? 'up' : 'down'" />
            </h-button>
            <h-button class="margin-right" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row class="button-row">
          <h-col :span="12" class="text-left">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" size="small" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 1200 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-button type="link" @click="showViolationDetails(record, false)">查看</h-button>
                <Actions :menu-options="getMenuOptions(record)" :on-menu-click="(e) => handleMenuClick(record, e)"
                  v-if="getMenuOptions(record).length > 0"></Actions>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <!-- 撤销确认弹框 -->
    <Modal v-model:visible="revokeVisible" title="撤销确认" @cancel="revokeVisible = false" :footer="null" width="400px">
      <div v-if="currentRecord">
        <div>考核单号：{{ currentRecord.examineCode }}</div>
        <div>商户名称：{{ currentRecord.merchantName }}</div>
        <div>考核条目：{{ currentRecord.entry }}</div>
        <div>考核类型：{{ currentRecord.type === 1 ? '违规' : currentRecord.type === 2 ? '整改' : '-' }}</div>
        <div>违规时间：{{ currentRecord.violationTime }}</div>
        <div style="font-weight: bold; margin-top: 16px">撤销确认</div>
        <div>撤销凭证：
          <h-upload v-model:fileList="revokeFileList" :custom-request="uploadRequest" :multiple="true" :max-count="1"
            @remove="handleRevokeFileRemove" accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
            :show-upload-list="true">
            <h-button>
              <upload-outlined />
              上传凭证
            </h-button>
          </h-upload>
        </div>
        <div class="footer-btns">
          <h-button @click="remove()">取消</h-button>
          <h-button type="primary" style="margin-left: 8px" :loading="revokeLoading"
            @click="handleConfirmRevoke">确定</h-button>
        </div>
      </div>
    </Modal>

    <!-- 查看取消原因弹框 -->
    <Modal v-model:visible="reasonVisible" title="撤销原因" @cancel="reasonVisible = false" :footer="null" width="400px">
      <div v-if="currentRecord">
        <div style="margin-top: 16px">撤销原因：{{ cancelReason }}</div>
        <div class="footer-btns">
          <h-button @click="reasonVisible = false">关闭</h-button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped lang="less">
.page-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.row-container {
  align-items: middle;
}

.col-margin {
  margin-bottom: 10px;
}

.search-row {
  align-items: middle;
  padding: 10px 10px 0px 10px;
}

.button-row {
  align-items: middle;
  padding: 10px 10px 0px 10px;
}

.label-right {
  text-align: right;
  margin-top: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 32px;
}

.margin-top {
  margin-top: 10px;
}

.margin-right {
  margin-right: 10px;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.full-width {
  width: 100%;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.footer-btns {
  margin-top: 24px;
  text-align: right;
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}
</style>
